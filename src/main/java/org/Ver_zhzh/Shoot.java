package org.Ver_zhzh;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import org.Ver_zhzh.shoot.ParticleHelper;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Fireball;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.ProjectileHitEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerInteractEntityEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerItemHeldEvent;
import org.bukkit.event.player.PlayerSwapHandItemsEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryView;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.Damageable;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.util.RayTraceResult;
import org.bukkit.util.Vector;



import net.citizensnpcs.api.CitizensAPI; // 添加ParticleHelper导入
import net.citizensnpcs.api.npc.NPC;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;

/**
 * Shoot 插件主类
 */
public class Shoot extends JavaPlugin implements Listener, CommandExecutor, TabCompleter {

    // 存储玩家每把武器的总弹药数量
    private Map<Player, Map<String, Integer>> ammoMap = new HashMap<>();

    // 存储玩家每把武器的实际弹夹子弹数（新增）
    private Map<Player, Map<String, Integer>> clipAmmoMap = new HashMap<>();

    // 存储玩家每把武器的换弹状态
    private Map<Player, Map<String, Boolean>> isReloadingMap = new HashMap<>();

    // 存储玩家每把武器的弹药耗尽状态（true表示弹药耗尽，false表示正常）
    private Map<Player, Map<String, Boolean>> gunEmptyStatusMap = new HashMap<>();

    // 存储玩家上一次左键点击的时间（用于双击检测）
    private Map<Player, Long> lastLeftClickMap = new HashMap<>();

    // 存储玩家的显示设置
    private Map<Player, DisplaySettings> displaySettingsMap = new HashMap<>();

    // 存储玩家当前手持的武器ID
    private Map<Player, String> currentGunMap = new HashMap<>();
    // 存储玩家的快速传送次数
    private Map<Player, Integer> teleportCountMap = new HashMap<>();

    // 存储玩家每把武器的冷却状态
    private Map<Player, Map<String, Boolean>> isCooldownMap = new HashMap<>();

    // 存储由插件发射的火焰弹及其对应的玩家
    private Map<Fireball, Player> fireballMap = new HashMap<>();

    private FileConfiguration config;
    // 新增配置文件
    private File playerDataFile;
    private File buyFile;
    private File locFile;
    private FileConfiguration playerDataConfig;
    private FileConfiguration buyConfig;
    private FileConfiguration locConfig;

    private ParticleHelper particleHelper; // 添加ParticleHelper实例

    // 静态实例变量
    private static Shoot instance;

    // 存储每个玩家的最大枪支数量 (UUID -> MaxGuns)
    private Map<UUID, Integer> playerMaxGuns = new HashMap<>();
    // 存储所有已定义的枪支材质，用于快速判断
    private Set<Material> gunMaterials = new HashSet<>();

    // 正在换弹的玩家列表
    private final Set<UUID> isReloading = new HashSet<>();

    // DeathZombieV4插件实例，用于获取显示设置
    private org.bukkit.plugin.Plugin deathZombiePlugin;

    @Override
    public void onEnable() {
        // 初始化插件实例
        instance = this;

        // 初始化ParticleHelper
        particleHelper = new ParticleHelper(this);
        getLogger().info("§a成功初始化ParticleHelper");

        // 初始化ParticleEffect（过渡类）
        org.Ver_zhzh.util.ParticleEffect.initParticleHelper(particleHelper);
        getLogger().info("§a成功初始化ParticleEffect兼容层");

        // 注册事件监听器和加载配置文件
        getServer().getPluginManager().registerEvents(this, this);
        saveDefaultConfig(); // 保存默认配置文件（如果不存在）
        config = getConfig(); // 获取配置文件
        getLogger().info(ChatColor.GREEN + "Shoot 插件已成功加载！");

// 初始化快速传送次数
        for (Player p : Bukkit.getOnlinePlayers()) {
            teleportCountMap.put(p, 0);
        }

        // 初始化新的配置文件
        initializeConfigs();

        // 初始化粒子效果辅助类
        particleHelper = new ParticleHelper(this);
        getLogger().info("§a粒子效果辅助类已初始化！");

        // 加载枪支材质列表
        loadGunMaterials();

        // 加载玩家数据
        loadPlayerData();

        // 尝试获取DeathZombieV4插件实例
        deathZombiePlugin = getServer().getPluginManager().getPlugin("DeathZombieV4");
        if (deathZombiePlugin != null) {
            getLogger().info("成功连接到DeathZombieV4插件，将同步显示设置");
        } else {
            getLogger().warning("未找到DeathZombieV4插件，将使用默认显示设置");
        }

        // 注册指令
        getCommand("shoot").setExecutor(this);
        getCommand("shoot").setTabCompleter(this);
        getCommand("byr").setExecutor(this);
        getCommand("byr").setTabCompleter(this);

        // 启动定期清理任务，每5秒检查一次重复枪支
        getServer().getScheduler().runTaskTimer(this, () -> {
            for (Player player : Bukkit.getOnlinePlayers()) {
                cleanupDuplicateGuns(player);
            }
        }, 100L, 100L); // 5秒后开始，每5秒执行一次

        getLogger().info("Shoot 插件已启用！");
    }

    /**
     * 初始化玩家数据、购买配置和位置配置文件
     */
    private void initializeConfigs() {
        // 初始化playerdata.yml
        playerDataFile = new File(getDataFolder(), "playerdata.yml");
        if (!playerDataFile.exists()) {
            playerDataFile.getParentFile().mkdirs();
            saveResource("playerdata.yml", false);
        }
        playerDataConfig = YamlConfiguration.loadConfiguration(playerDataFile);

        // 初始化Buy.yml
        buyFile = new File(getDataFolder(), "Buy.yml");
        if (!buyFile.exists()) {
            buyFile.getParentFile().mkdirs();
            saveResource("Buy.yml", false);
        }
        buyConfig = YamlConfiguration.loadConfiguration(buyFile);

        // 初始化loc.yml
        locFile = new File(getDataFolder(), "loc.yml");
        if (!locFile.exists()) {
            locFile.getParentFile().mkdirs();
            saveResource("loc.yml", false);
        }
        locConfig = YamlConfiguration.loadConfiguration(locFile);
    }

    /**
     * 保存所有配置文件
     */
    private void saveAllConfigs() {
        try {
            playerDataConfig.save(playerDataFile);
            buyConfig.save(buyFile);
            locConfig.save(locFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onDisable() {
        // 释放粒子效果资源
        if (particleHelper != null) {
            particleHelper.dispose();
            getLogger().info("§c已释放ParticleHelper资源");
        }

        // 释放ParticleEffect资源
        org.Ver_zhzh.util.ParticleEffect.dispose();
        getLogger().info("§c已释放ParticleEffect兼容层资源");

        getLogger().info("§c射击插件已卸载！");

        // 保存所有配置文件
        saveAllConfigs();
    }

    /**
     * 处理插件相关的指令
     *
     * @param sender 发送指令的执行者
     * @param command 执行的指令
     * @param label 指令标签
     * @param args 指令参数
     * @return 是否成功处理指令
     */
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 仅允许玩家执行指令
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "只有玩家可以执行该指令！");
            return true;
        }

        Player player = (Player) sender;

        // 检查指令是否为 /shoot
        if (label.equalsIgnoreCase("shoot")) {
            if (args.length == 0) {
                player.sendMessage(ChatColor.YELLOW + "使用: /shoot help " + ChatColor.WHITE + "查看指令帮助！");
                return true;
            }

            switch (args[0].toLowerCase()) {
                case "help":
                    // 显示帮助信息
                    player.sendMessage(ChatColor.AQUA + "====== " + ChatColor.GREEN + "Shoot 插件帮助" + ChatColor.AQUA + " ======");

                    // 普通玩家可用命令
                    player.sendMessage(ChatColor.GREEN + "普通玩家指令:");
                    player.sendMessage(ChatColor.YELLOW + "/shoot help" + ChatColor.WHITE + " - 查看帮助信息");
                    player.sendMessage(ChatColor.YELLOW + "/shoot display <start|hit> <on|off>" + ChatColor.WHITE + " - 控制标题显示");

                    // 管理员专用命令
                    if (player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "管理员专用指令:");
                        player.sendMessage(ChatColor.RED + "/shoot get <id|中文名>" + ChatColor.WHITE + " - 获取一把枪 " + ChatColor.GRAY + "(管理员)");
                        player.sendMessage(ChatColor.GRAY + "  例如: /shoot get id1 或 /shoot get 手枪");
                        player.sendMessage(ChatColor.RED + "/shoot up" + ChatColor.WHITE + " - 补充当前武器的子弹 " + ChatColor.GRAY + "(管理员)");
                        player.sendMessage(ChatColor.RED + "/shoot gui" + ChatColor.WHITE + " - 打开武器GUI界面 " + ChatColor.GRAY + "(管理员)");
                        player.sendMessage(ChatColor.RED + "/shoot reload" + ChatColor.WHITE + " - 重新加载Shoot配置 " + ChatColor.GRAY + "(管理员)");
                    } else {
                        player.sendMessage(ChatColor.GRAY + "更多指令需要管理员权限");
                    }

                    player.sendMessage(ChatColor.AQUA + "=======================================");
                    break;

                case "get":
                    // 获取一把指定ID或中文名称的武器 - 需要管理员权限
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限执行此操作！");
                        return true;
                    }

                    if (args.length < 2) {
                        player.sendMessage(ChatColor.RED + "请指定武器ID或中文名称！");
                        player.sendMessage(ChatColor.YELLOW + "例如: /shoot get id1 或 /shoot get 手枪");
                        return true;
                    }

                    String input = args[1];
                    String id = getGunIdByInput(input);

                    if (id != null && config.contains("guns." + id)) {
                        // 从配置中加载武器信息
                        String name = config.getString("guns." + id + ".name", "未知武器");
                        String materialName = config.getString("guns." + id + ".material", "WOOD_SPADE");
                        Material material = Material.getMaterial(materialName);
                        if (material == null) {
                            material = Material.WOODEN_SHOVEL; // 默认材质
                        }

                        // 创建武器物品
                        ItemStack gun = new ItemStack(material);
                        ItemMeta meta = gun.getItemMeta();
                        if (meta != null) {
                            meta.setDisplayName(ChatColor.GOLD + name); // 设置武器名称
                            gun.setItemMeta(meta); // 应用修改后的元数据
                        }

                        // 先清理重复的枪支，再添加新枪支
                        cleanupDuplicateGuns(player);

                        // 将武器添加到玩家的物品栏
                        player.getInventory().addItem(gun);
                        // 获取枪支弹夹容量和最大弹药量
                        int clipSize = config.getInt("guns." + id + ".clip_size", 30);
                        int maxAmmo = config.getInt("guns." + id + ".ammo", 300);

                        // 初始化弹药数量 - 使用正确的键名
                        ammoMap.putIfAbsent(player, new HashMap<>());
                        ammoMap.get(player).put(id + "_total", maxAmmo);

                        // 初始化弹夹子弹数量
                        clipAmmoMap.putIfAbsent(player, new HashMap<>());
                        clipAmmoMap.get(player).put(id, clipSize);

                        // 初始化枪支状态 - 设置为非弹药耗尽状态
                        gunEmptyStatusMap.putIfAbsent(player, new HashMap<>());
                        gunEmptyStatusMap.get(player).put(id, false);

                        // 初始化换弹状态
                        isReloadingMap.putIfAbsent(player, new HashMap<>());
                        isReloadingMap.get(player).put(id, false);

                        // 初始化冷却状态
                        isCooldownMap.putIfAbsent(player, new HashMap<>());
                        isCooldownMap.get(player).put(id, false);

                        // 初始化显示设置（从DeathZombieV4同步）
                        initializePlayerDisplaySettings(player);

                        // 设置物品数量为弹夹容量
                        gun.setAmount(clipSize);

                        // 更新物品描述
                        meta = gun.getItemMeta();
                        if (meta != null) {
                            List<String> lore = new ArrayList<>();
                            lore.add(ChatColor.GRAY + "武器类型: " + ChatColor.GREEN + "远程武器");
                            lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                            lore.add(ChatColor.GRAY + "弹夹容量: " + ChatColor.YELLOW + clipSize);
                            lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);

                            // 添加武器特定描述
                            addGunLore(lore, id);

                            lore.add(ChatColor.YELLOW + "右键点击射击，左键双击换弹");
                            meta.setLore(lore);
                            gun.setItemMeta(meta);
                        }

                        player.sendMessage(ChatColor.GREEN + "已获得武器: " + ChatColor.GOLD + name
                                + ChatColor.GREEN + " (弹夹容量: " + clipSize + ", 总弹药: " + maxAmmo + ")");

                        // 自动切换到新获得的武器
                        currentGunMap.put(player, id);

                        // 设置经验值显示
                        player.setLevel(maxAmmo);
                        player.setExp(1.0f);

                        // 打印调试信息
                        getLogger().info("为玩家 " + player.getName() + " 设置武器 " + id + " 的弹药: 弹夹=" + clipSize + ", 总弹药=" + maxAmmo);
                    } else {
                        player.sendMessage(ChatColor.RED + "无效的武器ID或名称: " + ChatColor.YELLOW + input);
                        player.sendMessage(ChatColor.GRAY + "可用的武器:");
                        showAvailableGuns(player);
                    }
                    break;

                case "up":
                    // 补充当前武器的弹药 - 需要管理员权限
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限执行此操作！");
                        return true;
                    }

                    String currentGunId = currentGunMap.get(player);
                    if (currentGunId == null) {
                        player.sendMessage(ChatColor.RED + "你手上没有武器！");
                        return true;
                    }

                    // 修复：检查正确的弹药键名（应该是 currentGunId + "_total"）
                    if (!ammoMap.containsKey(player) || !ammoMap.get(player).containsKey(currentGunId + "_total")) {
                        // 如果弹药数据不存在，初始化它
                        ammoMap.putIfAbsent(player, new HashMap<>());
                        int maxAmmo = config.getInt("guns." + currentGunId + ".ammo", 300);
                        ammoMap.get(player).put(currentGunId + "_total", maxAmmo);

                        // 同时初始化其他相关数据
                        clipAmmoMap.putIfAbsent(player, new HashMap<>());
                        int clipSize = config.getInt("guns." + currentGunId + ".clip_size", 30);
                        clipAmmoMap.get(player).put(currentGunId, clipSize);

                        gunEmptyStatusMap.putIfAbsent(player, new HashMap<>());
                        gunEmptyStatusMap.get(player).put(currentGunId, false);

                        player.sendMessage(ChatColor.YELLOW + "已为你初始化武器数据！");
                    }
                    int maxAmmo = config.getInt("guns." + currentGunId + ".ammo", 10);
                    ammoMap.get(player).put(currentGunId + "_total", maxAmmo);

                    // 重置枪支的弹药耗尽状态
                    if (gunEmptyStatusMap.containsKey(player)) {
                        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);
                        playerGunStatus.put(currentGunId, false);
                    }

                    // 检查玩家手中是否是钻石（弹药耗尽的武器）
                    ItemStack currentItem = player.getInventory().getItemInMainHand();
                    if (currentItem != null && currentItem.getType() == Material.DIAMOND && currentItem.getItemMeta() != null
                            && currentItem.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {

                        // 获取原始枪支ID和名称
                        String gunId = "";
                        String originalGunName = "";

                        // 从Lore中获取原始枪支ID
                        if (currentItem.getItemMeta().hasLore()) {
                            List<String> lore = currentItem.getItemMeta().getLore();
                            if (lore != null) {
                                for (String line : lore) {
                                    if (line.contains("gun_id:")) {
                                        gunId = ChatColor.stripColor(line).replace("gun_id:", "").trim();
                                        break;
                                    } else if (line.contains("原武器:")) {
                                        originalGunName = ChatColor.stripColor(line).replace("原武器:", "").trim();
                                    }
                                }
                            }
                        }

                        // 创建新的枪支物品
                        Material gunMaterial = Material.DIAMOND_HOE; // 默认材质

                        // 查找原始枪支的材质
                        ConfigurationSection gunsSection = config.getConfigurationSection("guns");
                        if (gunsSection != null) {
                            for (String gunKey : gunsSection.getKeys(false)) {
                                String gunName = config.getString("guns." + gunKey + ".name", "");
                                if (originalGunName.equals(gunName)) {
                                    String materialName = config.getString("guns." + gunKey + ".material", "DIAMOND_HOE");
                                    Material material = Material.getMaterial(materialName);
                                    if (material != null) {
                                        gunMaterial = material;
                                    }
                                    break;
                                }
                            }
                        }

                        // 创建新的枪支物品
                        ItemStack newGun = new ItemStack(gunMaterial);
                        ItemMeta newMeta = newGun.getItemMeta();
                        if (newMeta != null) {
                            // 设置显示名称
                            newMeta.setDisplayName(ChatColor.GOLD + originalGunName);

                            // 复制原来的Lore
                            if (currentItem.getItemMeta().hasLore()) {
                                List<String> lore = new ArrayList<>();
                                List<String> oldLore = currentItem.getItemMeta().getLore();

                                // 复制并修改Lore
                                if (oldLore != null) {
                                    for (String line : oldLore) {
                                        // 跳过弹药耗尽的提示信息
                                        if (line.contains("弹药已耗尽") || line.contains("请使用 /shoot up")) {
                                            continue;
                                        }

                                        // 更新弹药信息
                                        if (line.contains("总弹药")) {
                                            lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);
                                        } else if (line.contains("当前弹药")) {
                                            int clipSize = config.getInt("guns." + currentGunId + ".clip_size", 30);
                                            lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                                        } else {
                                            lore.add(line);
                                        }
                                    }
                                    newMeta.setLore(lore);
                                }
                            }
                            newGun.setItemMeta(newMeta);

                            // 设置弹夹子弹数
                            int clipSize = config.getInt("guns." + currentGunId + ".clip_size", 30);
                            newGun.setAmount(clipSize);

                            // 初始化弹夹数据结构
                            if (!clipAmmoMap.containsKey(player)) {
                                clipAmmoMap.put(player, new HashMap<>());
                            }
                            clipAmmoMap.get(player).put(currentGunId, clipSize);
                        }

                        // 替换玩家手中的物品
                        player.getInventory().setItemInMainHand(newGun);
                    }

                    player.sendMessage(ChatColor.GREEN + "你的武器已补充满子弹！");
                    updatePlayerXP(player, currentGunId);
                    break;

                case "display":
                    // 控制标题显示
                    if (args.length < 3) {
                        player.sendMessage(ChatColor.RED + "指令格式错误！正确格式: /shoot display <start|hit> <on|off>");
                        return true;
                    }

                    String displayType = args[1].toLowerCase();
                    String displayAction = args[2].toLowerCase();

                    // 确保玩家有显示设置，如果没有则从DeathZombieV4同步
                    initializePlayerDisplaySettings(player);
                    DisplaySettings settings = displaySettingsMap.get(player);

                    switch (displayType) {
                        case "start":
                            if (displayAction.equals("on")) {
                                settings.setStartTitle(true);
                                player.sendMessage(ChatColor.GREEN + "已开启射击开始时的标题显示！");
                            } else if (displayAction.equals("off")) {
                                settings.setStartTitle(false);
                                player.sendMessage(ChatColor.GREEN + "已关闭射击开始时的标题显示！");
                            } else {
                                player.sendMessage(ChatColor.RED + "无效的操作！使用 on 或 off。");
                            }
                            break;

                        case "hit":
                            if (displayAction.equals("on")) {
                                settings.setHitTitle(true);
                                player.sendMessage(ChatColor.GREEN + "已开启命中目标时的标题显示！");
                            } else if (displayAction.equals("off")) {
                                settings.setHitTitle(false);
                                player.sendMessage(ChatColor.GREEN + "已关闭命中目标时的标题显示！");
                            } else {
                                player.sendMessage(ChatColor.RED + "无效的操作！使用 on 或 off。");
                            }
                            break;

                        default:
                            player.sendMessage(ChatColor.RED + "无效的显示类型！使用 start 或 hit。");
                            return true;
                    }

                    displaySettingsMap.put(player, settings);
                    break;

                case "gui":
                    // 打开枪支和弹药的 GUI 界面 - 需要管理员权限
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限执行此操作！");
                        return true;
                    }

                    openShootGUI(player);
                    break;

                case "reload":
                    if (player.hasPermission("shoot.admin")) {
                        reloadShootConfig();
                        // 重新加载枪支材质列表
                        loadGunMaterials();
                        player.sendMessage(ChatColor.GREEN + "Shoot 插件配置已重新加载！");
                    } else {
                        player.sendMessage(ChatColor.RED + "你没有权限执行此操作！");
                    }
                    break;

                default:
                    // 未知指令
                    player.sendMessage(ChatColor.RED + "未知的子指令！使用: /shoot help 查看帮助");
                    break;
            }
        }

        // 检查指令是否为 /byr 或其别名
        if (label.equalsIgnoreCase("byr") || label.equalsIgnoreCase("buy") || label.equalsIgnoreCase("buypoint")) {
            if (args.length == 0) {
                player.sendMessage(ChatColor.YELLOW + "使用: /byr help 查看帮助信息");
                return true;
            }

            switch (args[0].toLowerCase()) {
                case "help":
                    if (player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.AQUA + "====== " + ChatColor.GREEN + "Buy Point 管理系统" + ChatColor.AQUA + " ======");
                        player.sendMessage(ChatColor.YELLOW + "/byr check" + ChatColor.WHITE + " - 查询自己的金钱余额");
                        player.sendMessage(ChatColor.YELLOW + "/byr set <类型> <物品ID或中文名> <名称> <价格>" + ChatColor.WHITE + " - 创建购买点");
                        player.sendMessage(ChatColor.GRAY + "  例如: /byr set 武器 手枪 商店手枪 150");
                        player.sendMessage(ChatColor.GRAY + "  例如: /byr set 护甲 皮革套装(上) 基础护甲 50");
                        player.sendMessage(ChatColor.YELLOW + "/byr remove" + ChatColor.WHITE + " - 移除购买点（看着NPC）");
                        player.sendMessage(ChatColor.YELLOW + "/byr buy" + ChatColor.WHITE + " - 打开购买测试界面");
                        player.sendMessage(ChatColor.YELLOW + "/byr list" + ChatColor.WHITE + " - 列出所有购买点");
                        player.sendMessage(ChatColor.YELLOW + "/byr add <金额> [玩家名]" + ChatColor.WHITE + " - 为玩家增加金钱");
                        player.sendMessage(ChatColor.GRAY + "类型: ar/护甲, wp/武器, it/道具, sp/特殊功能");
                    } else {
                        player.sendMessage(ChatColor.AQUA + "====== " + ChatColor.GREEN + "Buy Point 系统" + ChatColor.AQUA + " ======");
                        player.sendMessage(ChatColor.YELLOW + "/byr check" + ChatColor.WHITE + " - 查询自己的金钱余额");
                    }
                    player.sendMessage(ChatColor.AQUA + "=======================================");
                    break;

                case "check":
                    double money = getPlayerMoney(player);
                    player.sendMessage(ChatColor.GREEN + "你当前的金钱余额: " + ChatColor.GOLD + money + " 金币");
                    break;

                case "add":
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                        return true;
                    }

                    if (args.length < 2) {
                        player.sendMessage(ChatColor.RED + "用法: /byr add <金额> [玩家名]");
                        player.sendMessage(ChatColor.YELLOW + "例如: /byr add 100 或 /byr add 100 PlayerName");
                        return true;
                    }

                    double amount;
                    try {
                        amount = Double.parseDouble(args[1]);
                        if (amount <= 0) {
                            player.sendMessage(ChatColor.RED + "金额必须大于0！");
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        player.sendMessage(ChatColor.RED + "金额必须是一个有效的数字！");
                        return true;
                    }

                    Player targetPlayer = player;
                    if (args.length >= 3) {
                        targetPlayer = Bukkit.getPlayer(args[2]);
                        if (targetPlayer == null) {
                            player.sendMessage(ChatColor.RED + "玩家 " + args[2] + " 不在线或不存在！");
                            return true;
                        }
                    }

                    addPlayerMoney(targetPlayer, amount);
                    double newBalance = getPlayerMoney(targetPlayer);

                    if (targetPlayer == player) {
                        player.sendMessage(ChatColor.GREEN + "成功为自己增加了 " + ChatColor.GOLD + amount + " 金币");
                        player.sendMessage(ChatColor.GREEN + "当前余额: " + ChatColor.GOLD + newBalance + " 金币");
                    } else {
                        player.sendMessage(ChatColor.GREEN + "成功为玩家 " + ChatColor.YELLOW + targetPlayer.getName()
                                + ChatColor.GREEN + " 增加了 " + ChatColor.GOLD + amount + " 金币");
                        targetPlayer.sendMessage(ChatColor.GREEN + "管理员为你增加了 " + ChatColor.GOLD + amount + " 金币");
                        targetPlayer.sendMessage(ChatColor.GREEN + "当前余额: " + ChatColor.GOLD + newBalance + " 金币");
                    }
                    break;

                case "set":
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                        return true;
                    }

                    if (args.length < 5) {
                        player.sendMessage(ChatColor.RED + "用法: /byr set <类型> <物品ID或中文名> <名称> <价格>");
                        player.sendMessage(ChatColor.YELLOW + "类型: ar/护甲, wp/武器, it/道具, sp/特殊功能");
                        player.sendMessage(ChatColor.YELLOW + "例如: /byr set 武器 手枪 商店手枪 150");
                        return true;
                    }

                    String inputType = args[1];
                    String inputItemId = args[2];
                    String customName = args[3];
                    int price;
                    try {
                        price = Integer.parseInt(args[4]);
                    } catch (NumberFormatException e) {
                        player.sendMessage(ChatColor.RED + "价格必须是一个数字！");
                        return true;
                    }

                    // 解析类型
                    String type = getTypeByInput(inputType);
                    if (type == null) {
                        player.sendMessage(ChatColor.RED + "无效的类型: " + ChatColor.YELLOW + inputType);
                        player.sendMessage(ChatColor.GRAY + "可用类型: ar/护甲, wp/武器, it/道具, sp/特殊功能");
                        return true;
                    }

                    // 解析物品ID
                    String itemId = getBuyItemIdByInput(inputItemId, type);
                    if (itemId == null) {
                        player.sendMessage(ChatColor.RED + "无效的物品ID或名称: " + ChatColor.YELLOW + inputItemId);
                        showAvailableBuyItems(player, type);
                        return true;
                    }

                    Location loc = player.getLocation();
                    String npcName = ChatColor.GOLD + customName + ChatColor.GRAY + " (" + price + "金币)"; // 在NPC名称中显示价格
                    NPC npc = createBuyPointNPC(loc, npcName, type, itemId);

                    if (npc != null) {
                        player.sendMessage(ChatColor.GREEN + "成功创建购买点NPC: " + ChatColor.YELLOW + customName);
                    } else {
                        player.sendMessage(ChatColor.RED + "创建购买点失败！");
                    }
                    break;

                case "remove":
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                        return true;
                    }

                    // 使用rayTraceEntities方法替代getTargetEntity以兼容新版本API
                    RayTraceResult rayTrace = player.getWorld().rayTraceEntities(
                        player.getEyeLocation(),
                        player.getEyeLocation().getDirection(),
                        5.0,
                        entity -> entity != player && CitizensAPI.getNPCRegistry().isNPC(entity)
                    );

                    if (rayTrace != null && rayTrace.getHitEntity() != null) {
                        Entity targetEntity = rayTrace.getHitEntity();
                        NPC targetNPC = CitizensAPI.getNPCRegistry().getNPC(targetEntity);
                        String npcUUID = targetNPC.getUniqueId().toString();

                        if (locConfig.contains("npcs." + npcUUID)) {
                            targetNPC.destroy();
                            locConfig.set("npcs." + npcUUID, null);
                            saveAllConfigs();
                            player.sendMessage(ChatColor.GREEN + "成功移除购买点NPC！");
                        } else {
                            player.sendMessage(ChatColor.RED + "这不是一个购买点NPC！");
                        }
                    } else {
                        player.sendMessage(ChatColor.RED + "请直接看着要移除的NPC！");
                    }
                    break;

                case "buy":
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                        return true;
                    }

                    // 打开购买测试界面
                    openBuyGUI(player);
                    break;

                case "list":
                    if (!player.hasPermission("shoot.admin")) {
                        player.sendMessage(ChatColor.RED + "你没有权限使用此命令！");
                        return true;
                    }

                    // 列出所有购买点
                    ConfigurationSection npcSection = locConfig.getConfigurationSection("npcs");
                    if (npcSection == null || npcSection.getKeys(false).isEmpty()) {
                        player.sendMessage(ChatColor.YELLOW + "当前没有任何购买点！");
                        return true;
                    }

                    player.sendMessage(ChatColor.AQUA + "=== 购买点列表 ===");
                    for (String uuid : npcSection.getKeys(false)) {
                        String npcType = locConfig.getString("npcs." + uuid + ".type");
                        String npcItemId = locConfig.getString("npcs." + uuid + ".itemId");
                        Location npcLoc = new Location(
                                Bukkit.getWorld(locConfig.getString("npcs." + uuid + ".location.world")),
                                locConfig.getDouble("npcs." + uuid + ".location.x"),
                                locConfig.getDouble("npcs." + uuid + ".location.y"),
                                locConfig.getDouble("npcs." + uuid + ".location.z")
                        );
                        player.sendMessage(ChatColor.YELLOW + "类型: " + npcType
                                + ChatColor.WHITE + ", 物品ID: " + npcItemId
                                + ChatColor.GRAY + ", 位置: " + String.format("%.1f, %.1f, %.1f",
                                        npcLoc.getX(), npcLoc.getY(), npcLoc.getZ()));
                    }
                    break;

                default:
                    player.sendMessage(ChatColor.RED + "未知的子命令！使用 /byr help 查看帮助");
                    break;
            }
            return true;
        }

        return true;
    }

    /**
     * 监听玩家与实体交互事件，处理购买逻辑
     *
     * @param event 玩家与实体交互事件
     */
    @EventHandler
    public void onPlayerInteractEntity(PlayerInteractEntityEvent event) {
        Player player = event.getPlayer();
        Entity entity = event.getRightClicked();

        // 检查被点击的实体是否为NPC
        if (!CitizensAPI.getNPCRegistry().isNPC(entity)) {
            return;
        }

        NPC npc = CitizensAPI.getNPCRegistry().getNPC(entity);
        if (npc == null) {
            return;
        }

        String npcUUID = npc.getUniqueId().toString();
        if (!locConfig.contains("npcs." + npcUUID)) {
            return;
        }

        String type = locConfig.getString("npcs." + npcUUID + ".type");
        String itemId = locConfig.getString("npcs." + npcUUID + ".itemId");

        // 获取物品名称，用于后续提示
        String itemName = buyConfig.getString(type + "." + itemId + ".name", "未知物品");

        // 记录交互，方便调试
        getLogger().info("玩家 " + player.getName() + " 与NPC交互: 类型=" + type + ", 物品ID=" + itemId + ", 物品名称=" + itemName);

        // 检查玩家是否已经购买过（除非类型为it或sp）
        if (!type.equals("it") && !type.equals("sp")) {
            String playerUUID = player.getUniqueId().toString();
            String purchaseKey = "purchased." + npcUUID + "." + playerUUID;
            if (playerDataConfig.getBoolean(purchaseKey, false)) {
                player.sendMessage(ChatColor.RED + "你已经购买过此物品！");
                return;
            }
        }

        // 获取物品价格
        int price = buyConfig.getInt(type + "." + itemId + ".price", 0);
        if (price <= 0) {
            player.sendMessage(ChatColor.RED + "此物品不可购买或价格未设置！");
            return;
        }

        // 获取玩家余额
        double playerMoney = getPlayerMoney(player);

        if (playerMoney < price) {
            player.sendMessage(ChatColor.RED + "你的余额不足以购买此物品！需要 " + price + "，你只有 " + (int) playerMoney);
            return;
        }

        // 扣除玩家金额
        subtractPlayerMoney(player, price);

        // 根据类型和itemId处理购买逻辑
        boolean purchased = false;

        if (type.equals("wp")) {
            // 武器类型购买
            // 注意：id1现在是手枪，不再是铁剑
            player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.GOLD + itemName + ChatColor.GREEN + "，请检查你的物品栏！");
            giveGunToPlayer(player, itemId);
            purchased = true;
        } else if (type.equals("ar")) {
            // 护甲类型购买
            player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.AQUA + itemName + ChatColor.GREEN + "！");
            giveArmorToPlayer(player, itemId);
            purchased = true;
        } else if (type.equals("it")) {
            // 道具类型购买 - 为不同类型道具提供专门的提示

            // 判断物品类型并给出相应提示
            if (itemId.matches("id(3|4|5|6|7|8|9|10|11|12)")) {
                // 弹药补充提示
                player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.YELLOW + itemName + ChatColor.GREEN + "，正在为你的武器补充弹药...");
            } else if (itemId.equals("id1") || itemId.equals("id2")) {
                // 药水效果提示
                player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.LIGHT_PURPLE + itemName + ChatColor.GREEN + " 效果！");
            } else {
                // 普通物品提示
                player.sendMessage(ChatColor.GREEN + "你已购买 " + ChatColor.AQUA + itemName + ChatColor.GREEN + "，已添加到你的物品栏！");
            }

            // 给予道具
            giveItemToPlayer(player, itemId);
            purchased = true;
        } else if (type.equals("sp")) {
            // 特殊功能类型购买
            player.sendMessage(ChatColor.GREEN + "你已激活特殊功能: " + ChatColor.GOLD + itemName + ChatColor.GREEN + "！");
            activateSpecialFunction(itemId);
            purchased = true;
        }

        // 标记玩家已购买（除非类型为it或sp）
        if (!type.equals("it") && !type.equals("sp") && purchased) {
            String playerUUID = player.getUniqueId().toString();
            String purchaseKey = "purchased." + npcUUID + "." + playerUUID;
            playerDataConfig.set(purchaseKey, true);
            getLogger().info("标记玩家 " + player.getName() + " 已购买物品: " + type + "." + itemId);
        }

        // 保存配置
        saveAllConfigs();

        // 发送声音效果（可选）
        String purchaseSound = buyConfig.getString(type + "." + itemId + ".purchase_sound", "");
        if (!purchaseSound.isEmpty()) {
            playSoundSafely(player, player.getLocation(), purchaseSound, "ENTITY_PLAYER_LEVELUP", 1.0f, 1.0f);
        } else {
            // 默认购买音效
            playSoundSafely(player, player.getLocation(), "ENTITY_EXPERIENCE_ORB_PICKUP", "ENTITY_PLAYER_LEVELUP", 1.0f, 1.0f);
        }
    }

    /**
     * 给玩家提供非枪支武器（如铁剑）
     *
     * @param player 目标玩家
     * @param itemId 武器ID
     */
    public void giveNonGunWeaponToPlayer(Player player, String itemId) {
        if (buyConfig.contains("wp." + itemId)) {
            String itemName = buyConfig.getString("wp." + itemId + ".name", "未知武器");

            // 获取材质
            Material material;
            // 从配置中获取材质
            String materialName = buyConfig.getString("wp." + itemId + ".material", "WOODEN_SWORD");
            material = Material.getMaterial(materialName);
            if (material == null) {
                material = Material.WOODEN_SWORD; // 默认材质
                getLogger().warning("无法找到材质 " + materialName + "，使用默认材质 WOODEN_SWORD");
            }

            // 创建武器物品
            ItemStack weapon = new ItemStack(material);
            ItemMeta meta = weapon.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + itemName);

                // 添加物品描述
                meta.setLore(Arrays.asList(
                        ChatColor.GRAY + "武器类型: " + "近战武器",
                        ChatColor.YELLOW + "左键点击攻击"
                ));

                weapon.setItemMeta(meta);
            }

            // 如果有附魔，应用附魔
            if (buyConfig.contains("wp." + itemId + ".enchantments")) {
                for (String enchantName : buyConfig.getConfigurationSection("wp." + itemId + ".enchantments").getKeys(false)) {
                    Enchantment enchant = Enchantment.getByName(enchantName);
                    if (enchant != null) {
                        int level = buyConfig.getInt("wp." + itemId + ".enchantments." + enchantName, 1);
                        weapon.addUnsafeEnchantment(enchant, level);
                    }
                }
            }

            // 播放购买声音
            String purchaseSound = buyConfig.getString("wp." + itemId + ".sound", "ENTITY_PLAYER_LEVELUP");
            playSoundSafely(player, player.getLocation(), purchaseSound, "ENTITY_PLAYER_LEVELUP", 1.0f, 1.0f);

            // 将武器添加到玩家的物品栏
            player.getInventory().addItem(weapon);
            player.sendMessage(ChatColor.GREEN + "已获得: " + ChatColor.GOLD + itemName);
            getLogger().info("已为玩家 " + player.getName() + " 添加武器: " + itemName + " (类型: 非枪支武器)");
        } else {
            player.sendMessage(ChatColor.RED + "无效的武器ID！");
            getLogger().warning("尝试给予玩家 " + player.getName() + " 无效的武器ID: " + itemId);
        }
    }

    /**
     * 给玩家装备指定ID的护甲
     *
     * @param player 目标玩家
     * @param itemId 护甲ID
     */
    public void giveArmorToPlayer(Player player, String itemId) {
        if (buyConfig.contains("ar." + itemId)) {
            // 获取护甲套装信息
            String name = buyConfig.getString("ar." + itemId + ".name", "未知护甲");

            if (itemId.equals("id1")) { // 皮革套装(上)
                // 创建皮革头盔
                ItemStack helmet = new ItemStack(Material.LEATHER_HELMET);
                ItemMeta helmetMeta = helmet.getItemMeta();
                if (helmetMeta != null) {
                    helmetMeta.setDisplayName(ChatColor.GOLD + name + " 头盔");
                    helmet.setItemMeta(helmetMeta);
                }

                // 创建皮革胸甲
                ItemStack chestplate = new ItemStack(Material.LEATHER_CHESTPLATE);
                ItemMeta chestMeta = chestplate.getItemMeta();
                if (chestMeta != null) {
                    chestMeta.setDisplayName(ChatColor.GOLD + name + " 胸甲");
                    chestplate.setItemMeta(chestMeta);
                }

                // 装备护甲（替换原有的头盔和胸甲）
                player.getInventory().setHelmet(helmet);
                player.getInventory().setChestplate(chestplate);

                player.sendMessage(ChatColor.GREEN + "已装备护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id2")) { // 皮革套装(下)
                // 创建皮革护腿
                ItemStack leggings = new ItemStack(Material.LEATHER_LEGGINGS);
                ItemMeta leggingsMeta = leggings.getItemMeta();
                if (leggingsMeta != null) {
                    leggingsMeta.setDisplayName(ChatColor.GOLD + name + " 护腿");
                    leggings.setItemMeta(leggingsMeta);
                }

                // 创建皮革靴子
                ItemStack boots = new ItemStack(Material.LEATHER_BOOTS);
                ItemMeta bootsMeta = boots.getItemMeta();
                if (bootsMeta != null) {
                    bootsMeta.setDisplayName(ChatColor.GOLD + name + " 靴子");
                    boots.setItemMeta(bootsMeta);
                }

                // 装备护甲（替换原有的护腿和靴子）
                player.getInventory().setLeggings(leggings);
                player.getInventory().setBoots(boots);

                player.sendMessage(ChatColor.GREEN + "已装备护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id3")) { // 锁链套装(上)
                // 创建锁链头盔
                ItemStack helmet = new ItemStack(Material.CHAINMAIL_HELMET);
                ItemMeta helmetMeta = helmet.getItemMeta();
                if (helmetMeta != null) {
                    helmetMeta.setDisplayName(ChatColor.GOLD + name + " 头盔");
                    helmet.setItemMeta(helmetMeta);
                }

                // 创建锁链胸甲
                ItemStack chestplate = new ItemStack(Material.CHAINMAIL_CHESTPLATE);
                ItemMeta chestMeta = chestplate.getItemMeta();
                if (chestMeta != null) {
                    chestMeta.setDisplayName(ChatColor.GOLD + name + " 胸甲");
                    chestplate.setItemMeta(chestMeta);
                }

                // 装备护甲（替换原有的头盔和胸甲）
                player.getInventory().setHelmet(helmet);
                player.getInventory().setChestplate(chestplate);

                player.sendMessage(ChatColor.GREEN + "已装备锁链护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id4")) { // 锁链套装(下)
                // 创建锁链护腿
                ItemStack leggings = new ItemStack(Material.CHAINMAIL_LEGGINGS);
                ItemMeta leggingsMeta = leggings.getItemMeta();
                if (leggingsMeta != null) {
                    leggingsMeta.setDisplayName(ChatColor.GOLD + name + " 护腿");
                    leggings.setItemMeta(leggingsMeta);
                }

                // 创建锁链靴子
                ItemStack boots = new ItemStack(Material.CHAINMAIL_BOOTS);
                ItemMeta bootsMeta = boots.getItemMeta();
                if (bootsMeta != null) {
                    bootsMeta.setDisplayName(ChatColor.GOLD + name + " 靴子");
                    boots.setItemMeta(bootsMeta);
                }

                // 装备护甲（替换原有的护腿和靴子）
                player.getInventory().setLeggings(leggings);
                player.getInventory().setBoots(boots);

                player.sendMessage(ChatColor.GREEN + "已装备锁链护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id5")) { // 铁套装(上)
                // 创建铁头盔
                ItemStack helmet = new ItemStack(Material.IRON_HELMET);
                ItemMeta helmetMeta = helmet.getItemMeta();
                if (helmetMeta != null) {
                    helmetMeta.setDisplayName(ChatColor.GOLD + name + " 铁头盔");
                    helmet.setItemMeta(helmetMeta);
                }

                // 创建铁胸甲
                ItemStack chestplate = new ItemStack(Material.IRON_CHESTPLATE);
                ItemMeta chestMeta = chestplate.getItemMeta();
                if (chestMeta != null) {
                    chestMeta.setDisplayName(ChatColor.GOLD + name + " 铁胸甲");
                    chestplate.setItemMeta(chestMeta);
                }

                // 装备护甲（替换原有的头盔和胸甲）
                player.getInventory().setHelmet(helmet);
                player.getInventory().setChestplate(chestplate);

                player.sendMessage(ChatColor.GREEN + "已装备铁套装: " + ChatColor.GOLD + name);
            } // 新增：铁套装(下)
            else if (itemId.equals("id6")) { // 铁套装(下)
                // 创建铁护腿
                ItemStack leggings = new ItemStack(Material.IRON_LEGGINGS);
                ItemMeta leggingsMeta = leggings.getItemMeta();
                if (leggingsMeta != null) {
                    leggingsMeta.setDisplayName(ChatColor.GOLD + name + " 铁护腿");
                    leggings.setItemMeta(leggingsMeta);
                }

                // 创建铁靴子
                ItemStack boots = new ItemStack(Material.IRON_BOOTS);
                ItemMeta bootsMeta = boots.getItemMeta();
                if (bootsMeta != null) {
                    bootsMeta.setDisplayName(ChatColor.GOLD + name + " 铁靴子");
                    boots.setItemMeta(bootsMeta);
                }

                // 装备护甲（替换原有的护腿和靴子）
                player.getInventory().setLeggings(leggings);
                player.getInventory().setBoots(boots);

                player.sendMessage(ChatColor.GREEN + "已装备铁套装(下): " + ChatColor.GOLD + name);
            } else if (itemId.equals("id7")) { // 钻石套装(上)
                // 创建钻石头盔
                ItemStack helmet = new ItemStack(Material.DIAMOND_HELMET);
                ItemMeta helmetMeta = helmet.getItemMeta();
                if (helmetMeta != null) {
                    helmetMeta.setDisplayName(ChatColor.GOLD + name + " 钻石头盔");
                    helmet.setItemMeta(helmetMeta);
                }

                // 创建钻石胸甲
                ItemStack chestplate = new ItemStack(Material.DIAMOND_CHESTPLATE);
                ItemMeta chestMeta = chestplate.getItemMeta();
                if (chestMeta != null) {
                    chestMeta.setDisplayName(ChatColor.GOLD + name + " 钻石胸甲");
                    chestplate.setItemMeta(chestMeta);
                }

                // 装备护甲（替换原有的头盔和胸甲）
                player.getInventory().setHelmet(helmet);
                player.getInventory().setChestplate(chestplate);

                player.sendMessage(ChatColor.GREEN + "已装备钻石护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id8")) { // 钻石套装(下)
                // 创建钻石护腿
                ItemStack leggings = new ItemStack(Material.DIAMOND_LEGGINGS);
                ItemMeta leggingsMeta = leggings.getItemMeta();
                if (leggingsMeta != null) {
                    leggingsMeta.setDisplayName(ChatColor.GOLD + name + " 钻石护腿");
                    leggings.setItemMeta(leggingsMeta);
                }

                // 创建钻石靴子
                ItemStack boots = new ItemStack(Material.DIAMOND_BOOTS);
                ItemMeta bootsMeta = boots.getItemMeta();
                if (bootsMeta != null) {
                    bootsMeta.setDisplayName(ChatColor.GOLD + name + " 钻石靴子");
                    boots.setItemMeta(bootsMeta);
                }

                // 装备护甲（替换原有的护腿和靴子）
                player.getInventory().setLeggings(leggings);
                player.getInventory().setBoots(boots);

                player.sendMessage(ChatColor.GREEN + "已装备钻石套装(下): " + ChatColor.GOLD + name);
            } else if (itemId.equals("id9")) { // 附魔钻石套装1号(上)
                // 创建附魔钻石头盔
                ItemStack helmet = new ItemStack(Material.DIAMOND_HELMET);
                ItemMeta helmetMeta = helmet.getItemMeta();
                if (helmetMeta != null) {
                    helmetMeta.setDisplayName(ChatColor.GOLD + name + " 附魔头盔");
                    helmetMeta.addEnchant(Enchantment.PROTECTION, 1, true); // 添加保护1附魔
                    helmet.setItemMeta(helmetMeta);
                }

                // 创建附魔钻石胸甲
                ItemStack chestplate = new ItemStack(Material.DIAMOND_CHESTPLATE);
                ItemMeta chestMeta = chestplate.getItemMeta();
                if (chestMeta != null) {
                    chestMeta.setDisplayName(ChatColor.GOLD + name + " 附魔胸甲");
                    chestMeta.addEnchant(Enchantment.PROTECTION, 1, true); // 添加保护1附魔
                    chestplate.setItemMeta(chestMeta);
                }

                // 装备护甲（替换原有的头盔和胸甲）
                player.getInventory().setHelmet(helmet);
                player.getInventory().setChestplate(chestplate);

                player.sendMessage(ChatColor.GREEN + "已装备附魔钻石护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id10")) { // 附魔钻石套装1号(下)
                // 创建附魔钻石护腿
                ItemStack leggings = new ItemStack(Material.DIAMOND_LEGGINGS);
                ItemMeta leggingsMeta = leggings.getItemMeta();
                if (leggingsMeta != null) {
                    leggingsMeta.setDisplayName(ChatColor.GOLD + name + " 附魔护腿");
                    leggingsMeta.addEnchant(Enchantment.PROTECTION, 1, true); // 添加保护1附魔
                    leggings.setItemMeta(leggingsMeta);
                }

                // 创建附魔钻石靴子
                ItemStack boots = new ItemStack(Material.DIAMOND_BOOTS);
                ItemMeta bootsMeta = boots.getItemMeta();
                if (bootsMeta != null) {
                    bootsMeta.setDisplayName(ChatColor.GOLD + name + " 附魔靴子");
                    bootsMeta.addEnchant(Enchantment.PROTECTION, 1, true); // 添加保护1附魔
                    boots.setItemMeta(bootsMeta);
                }

                // 装备护甲（替换原有的护腿和靴子）
                player.getInventory().setLeggings(leggings);
                player.getInventory().setBoots(boots);

                player.sendMessage(ChatColor.GREEN + "已装备附魔钻石护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id11")) { // 附魔钻石套装2号(上)
                // 创建附魔钻石头盔
                ItemStack helmet = new ItemStack(Material.DIAMOND_HELMET);
                ItemMeta helmetMeta = helmet.getItemMeta();
                if (helmetMeta != null) {
                    helmetMeta.setDisplayName(ChatColor.GOLD + name + " 附魔头盔");
                    helmetMeta.addEnchant(Enchantment.PROTECTION, 2, true); // 添加保护2附魔
                    helmet.setItemMeta(helmetMeta);
                }

                // 创建附魔钻石胸甲
                ItemStack chestplate = new ItemStack(Material.DIAMOND_CHESTPLATE);
                ItemMeta chestMeta = chestplate.getItemMeta();
                if (chestMeta != null) {
                    chestMeta.setDisplayName(ChatColor.GOLD + name + " 附魔胸甲");
                    chestMeta.addEnchant(Enchantment.PROTECTION, 2, true); // 添加保护2附魔
                    chestplate.setItemMeta(chestMeta);
                }

                // 装备护甲（替换原有的头盔和胸甲）
                player.getInventory().setHelmet(helmet);
                player.getInventory().setChestplate(chestplate);

                player.sendMessage(ChatColor.GREEN + "已装备附魔钻石护甲套装: " + ChatColor.GOLD + name);
            } else if (itemId.equals("id12")) { // 附魔钻石套装2号(下)
                // 创建附魔钻石护腿
                ItemStack leggings = new ItemStack(Material.DIAMOND_LEGGINGS);
                ItemMeta leggingsMeta = leggings.getItemMeta();
                if (leggingsMeta != null) {
                    leggingsMeta.setDisplayName(ChatColor.GOLD + name + " 附魔护腿");
                    leggingsMeta.addEnchant(Enchantment.PROTECTION, 2, true); // 添加保护2附魔
                    leggings.setItemMeta(leggingsMeta);
                }

                // 创建附魔钻石靴子
                ItemStack boots = new ItemStack(Material.DIAMOND_BOOTS);
                ItemMeta bootsMeta = boots.getItemMeta();
                if (bootsMeta != null) {
                    bootsMeta.setDisplayName(ChatColor.GOLD + name + " 附魔靴子");
                    bootsMeta.addEnchant(Enchantment.PROTECTION, 2, true); // 添加保护2附魔
                    boots.setItemMeta(bootsMeta);
                }

                // 装备护甲（替换原有的护腿和靴子）
                player.getInventory().setLeggings(leggings);
                player.getInventory().setBoots(boots);

                player.sendMessage(ChatColor.GREEN + "已装备附魔钻石护甲套装: " + ChatColor.GOLD + name);
            } else {
                player.sendMessage(ChatColor.RED + "无效的护甲ID！");
            }
        } else {
            player.sendMessage(ChatColor.RED + "无效的护甲ID！");
        }
    }

    /**
     * 给玩家添加指定ID的道具
     *
     * @param player 目标玩家
     * @param itemId 道具ID
     */
    public void giveItemToPlayer(Player player, String itemId) {
        if (buyConfig.contains("it." + itemId)) {
            String itemName = buyConfig.getString("it." + itemId + ".name", "未知道具");

            // 处理弹药补充逻辑
            Map<String, String> ammoToGunMap = new HashMap<>();
            // 弹药映射关系
            ammoToGunMap.put("id3", "id1"); // 手枪弹药 -> 手枪(id1)
            ammoToGunMap.put("id4", "id2"); // 步枪弹药 -> 步枪(id2)
            ammoToGunMap.put("id4", "id11"); // 步枪弹药 -> 突击步枪(id11)，使用相同弹药
            ammoToGunMap.put("id5", "id3"); // 霰弹枪弹药 -> 霰弹枪(id3)
            ammoToGunMap.put("id6", "id4"); // 机枪弹药 -> 机枪(id4)
            ammoToGunMap.put("id7", "id5"); // 火箭筒弹药 -> 火箭筒(id5)
            ammoToGunMap.put("id8", "id6"); // 电击枪弹药 -> 电击枪(id6)
            ammoToGunMap.put("id9", "id7"); // 狙击步枪弹药 -> 狙击步枪(id7)
            ammoToGunMap.put("id10", "id8"); // 冰冻枪弹药 -> 冰冻枪(id8)
            ammoToGunMap.put("id11", "id9"); // 雷击枪弹药 -> 雷击枪(id9)
            ammoToGunMap.put("id12", "id10"); // 压强枪弹药 -> 压强枪(id10)
            // 新增弹药映射
            ammoToGunMap.put("id15", "id10"); // 压强枪弹药
            ammoToGunMap.put("id16", "id13"); // 等离子枪弹药
            ammoToGunMap.put("id17", "id14"); // 死神收割者弹药
            ammoToGunMap.put("id18", "id15"); // 毁灭者弹药
            ammoToGunMap.put("id19", "id16"); // 超级激光炮弹药
            ammoToGunMap.put("id20", "id17"); // 黑洞吞噬者弹药
            ammoToGunMap.put("id21", "id18"); // 音轨步枪弹药
            ammoToGunMap.put("id22", "id19"); // 能量脉冲枪弹药
            ammoToGunMap.put("id23", "id20"); // 彩虹喷射器弹药
            ammoToGunMap.put("id24", "id21"); // 传送枪弹药
            ammoToGunMap.put("id25", "id22"); // 星辉主宰者弹药
            ammoToGunMap.put("id26", "id23"); // 虚空星尘弹药
            ammoToGunMap.put("id27", "id24"); // 循声炮弹药

            // 检查是否是弹药道具
            if (ammoToGunMap.containsKey(itemId)) {
                // 获取当前手持武器ID
                String currentGunId = getPlayerGunId(player);

                // 如果玩家当前未持有武器
                if (currentGunId == null) {
                    // 尝试在物品栏中寻找对应的武器
                    boolean foundGun = false;
                    for (ItemStack item : player.getInventory().getContents()) {
                        if (item != null && item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                            String displayName = ChatColor.stripColor(item.getItemMeta().getDisplayName());
                            String expectedGunId = ammoToGunMap.get(itemId);
                            String gunName = config.getString("guns." + expectedGunId + ".name");
                            if (displayName.equals(gunName)) {
                                currentGunId = expectedGunId;
                                foundGun = true;
                                break;
                            }
                        }
                    }

                    if (!foundGun) {
                        player.sendMessage(ChatColor.RED + "你没有可以补充此弹药的武器！");
                        return;
                    }
                }

                // 如果当前枪支类型与弹药类型不匹配
                String expectedGunId = ammoToGunMap.get(itemId);
                if (!expectedGunId.equals(currentGunId)) {
                    player.sendMessage(ChatColor.RED + "这种弹药不适合你当前的武器！");
                    return;
                }

                // 补充弹药
                ammoMap.putIfAbsent(player, new HashMap<>());
                int maxAmmo = config.getInt("guns." + currentGunId + ".ammo", 10);
                ammoMap.get(player).put(currentGunId + "_total", maxAmmo);

                // 初始化枪支状态数据结构
                if (!gunEmptyStatusMap.containsKey(player)) {
                    gunEmptyStatusMap.put(player, new HashMap<>());
                }

                // 重置枪支弹药耗尽状态
                gunEmptyStatusMap.get(player).put(currentGunId, false);

                // 检查玩家手中是否是钻石（弹药耗尽的武器）
                ItemStack currentItem = player.getInventory().getItemInMainHand();
                if (currentItem != null && currentItem.getType() == Material.DIAMOND && currentItem.getItemMeta() != null
                        && currentItem.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {

                    // 获取原始枪支ID和名称
                    String gunId = "";
                    String originalGunName = "";

                    // 从Lore中获取原始枪支ID
                    if (currentItem.getItemMeta().hasLore()) {
                        List<String> lore = currentItem.getItemMeta().getLore();
                        if (lore != null) {
                            for (String line : lore) {
                                if (line.contains("gun_id:")) {
                                    gunId = ChatColor.stripColor(line).replace("gun_id:", "").trim();
                                    break;
                                } else if (line.contains("原武器:")) {
                                    originalGunName = ChatColor.stripColor(line).replace("原武器:", "").trim();
                                }
                            }
                        }
                    }

                    // 创建新的枪支物品
                    Material gunMaterial = Material.DIAMOND_HOE; // 默认材质

                    // 查找原始枪支的材质
                    ConfigurationSection gunsSection = config.getConfigurationSection("guns");
                    if (gunsSection != null) {
                        for (String gunKey : gunsSection.getKeys(false)) {
                            String gunName = config.getString("guns." + gunKey + ".name", "");
                            if (originalGunName.equals(gunName)) {
                                String materialName = config.getString("guns." + gunKey + ".material", "DIAMOND_HOE");
                                Material material = Material.getMaterial(materialName);
                                if (material != null) {
                                    gunMaterial = material;
                                }
                                break;
                            }
                        }
                    }

                    // 创建新的枪支物品
                    ItemStack newGun = new ItemStack(gunMaterial);
                    ItemMeta newMeta = newGun.getItemMeta();
                    if (newMeta != null) {
                        // 设置显示名称
                        newMeta.setDisplayName(ChatColor.GOLD + originalGunName);

                        // 复制原来的Lore
                        if (currentItem.getItemMeta().hasLore()) {
                            List<String> lore = new ArrayList<>();
                            List<String> oldLore = currentItem.getItemMeta().getLore();

                            // 复制并修改Lore
                            if (oldLore != null) {
                                for (String line : oldLore) {
                                    // 跳过弹药耗尽的提示信息
                                    if (line.contains("弹药已耗尽") || line.contains("请使用 /shoot up")) {
                                        continue;
                                    }

                                    // 更新弹药信息
                                    if (line.contains("总弹药")) {
                                        lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);
                                    } else if (line.contains("当前弹药")) {
                                        int clipSize = config.getInt("guns." + currentGunId + ".clip_size", 30);
                                        lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                                    } else {
                                        lore.add(line);
                                    }
                                }
                                newMeta.setLore(lore);
                            }
                        }
                        newGun.setItemMeta(newMeta);

                        // 设置弹夹子弹数
                        int clipSize = config.getInt("guns." + currentGunId + ".clip_size", 30);
                        // 设置物品数量为弹夹容量（修复bug：确保枪支获取后是满子弹状态）
                        newGun.setAmount(clipSize);

                        // 初始化弹夹数据结构
                        if (!clipAmmoMap.containsKey(player)) {
                            clipAmmoMap.put(player, new HashMap<>());
                        }
                        clipAmmoMap.get(player).put(currentGunId, clipSize);
                    }

                    // 替换玩家手中的物品
                    player.getInventory().setItemInMainHand(newGun);

                    // 重置枪支的弹药耗尽状态
                    if (gunEmptyStatusMap.containsKey(player)) {
                        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);
                        playerGunStatus.put(currentGunId, false);
                    }
                }

                updatePlayerXP(player, currentGunId);
                player.sendMessage(ChatColor.GREEN + "你的武器弹药已补充满！");
                playSoundSafely(player, player.getLocation(), "ENTITY_EXPERIENCE_ORB_PICKUP", "ENTITY_EXPERIENCE_ORB_PICKUP", 1.0f, 1.0f);
                return;
            }

            // 处理药水效果道具（id1和id2）
            if (itemId.equals("id1")) {
                // 速度buff1药水 - 创建物理药水物品
                ItemStack potion = new ItemStack(Material.POTION);
                org.bukkit.inventory.meta.PotionMeta meta = (org.bukkit.inventory.meta.PotionMeta) potion.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ChatColor.LIGHT_PURPLE + itemName);
                    List<String> lore = new ArrayList<>();
                    lore.add(ChatColor.GRAY + "饮用后获得30秒速度提升效果");
                    lore.add(ChatColor.YELLOW + "在僵尸末日中生存的必备道具");
                    meta.setLore(lore);
                    // 设置药水颜色和效果
                    meta.setColor(org.bukkit.Color.AQUA); // 设置为浓蓝色
                    meta.addCustomEffect(new PotionEffect(PotionEffectType.SPEED, 30 * 20, 0), true);
                    potion.setItemMeta(meta);
                }
                player.getInventory().addItem(potion);
                player.sendMessage(ChatColor.GREEN + "已获得: " + ChatColor.LIGHT_PURPLE + itemName);
                playSoundSafely(player, player.getLocation(), "ENTITY_ITEM_PICKUP", "ENTITY_ITEM_PICKUP", 1.0f, 1.0f);
            } else if (itemId.equals("id2")) {
                // 跳跃buff1药水 - 创建物理药水物品
                ItemStack potion = new ItemStack(Material.POTION);
                org.bukkit.inventory.meta.PotionMeta meta = (org.bukkit.inventory.meta.PotionMeta) potion.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ChatColor.LIGHT_PURPLE + itemName);
                    List<String> lore = new ArrayList<>();
                    lore.add(ChatColor.GRAY + "饮用后获得30秒跳跃提升效果");
                    lore.add(ChatColor.YELLOW + "在僵尸末日中逃生的必备道具");
                    meta.setLore(lore);
                    // 设置药水颜色和效果
                    meta.setColor(org.bukkit.Color.GREEN); // 设置为绿色
                    meta.addCustomEffect(new PotionEffect(PotionEffectType.JUMP_BOOST, 30 * 20, 0), true);
                    potion.setItemMeta(meta);
                }
                player.getInventory().addItem(potion);
                player.sendMessage(ChatColor.GREEN + "已获得: " + ChatColor.LIGHT_PURPLE + itemName);
                playSoundSafely(player, player.getLocation(), "ENTITY_ITEM_PICKUP", "ENTITY_ITEM_PICKUP", 1.0f, 1.0f);
            } else if (itemId.equals("id13")) {
                // 金苹果
                ItemStack item = new ItemStack(Material.GOLDEN_APPLE);
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ChatColor.GOLD + itemName);
                    item.setItemMeta(meta);
                }
                player.getInventory().addItem(item);
                player.sendMessage(ChatColor.GREEN + "已获得: " + ChatColor.GOLD + itemName);
                playSoundSafely(player, player.getLocation(), "ENTITY_ITEM_PICKUP", "ENTITY_ITEM_PICKUP", 1.0f, 1.0f);
            } else if (itemId.equals("id14")) {
                // 武器凭证
                ItemStack item = new ItemStack(Material.PAPER);
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ChatColor.AQUA + itemName);
                    List<String> lore = new ArrayList<>();
                    lore.add(ChatColor.GRAY + "持有此凭证，证明你是真正的枪械大师！");
                    lore.add(ChatColor.GREEN + "效果: 最大枪支携带数量提升至 3。");
                    meta.setLore(lore);
                    item.setItemMeta(meta);
                }
                player.getInventory().addItem(item);
                player.sendMessage(ChatColor.GREEN + "已获得: " + ChatColor.AQUA + itemName);
                playSoundSafely(player, player.getLocation(), "ENTITY_PLAYER_LEVELUP", "ENTITY_PLAYER_LEVELUP", 1.0f, 1.0f);
            } else if (itemId.equals("id28")) {
                // 熟牛排
                ItemStack item = new ItemStack(Material.COOKED_BEEF);
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ChatColor.GOLD + itemName);
                    List<String> lore = new ArrayList<>();
                    lore.add(ChatColor.GRAY + "美味的熟牛排，可以恢复生命值");
                    lore.add(ChatColor.GREEN + "效果: 恢复 8 点生命值");
                    meta.setLore(lore);
                    item.setItemMeta(meta);
                }
                player.getInventory().addItem(item);
                player.sendMessage(ChatColor.GREEN + "已获得: " + ChatColor.GOLD + itemName);
                playSoundSafely(player, player.getLocation(), "ENTITY_ITEM_PICKUP", "ENTITY_ITEM_PICKUP", 1.0f, 1.0f);
            } else if (itemId.equals("id29")) {
                // 治疗药水(1级)
                ItemStack potion = new ItemStack(Material.POTION);
                org.bukkit.inventory.meta.PotionMeta meta = (org.bukkit.inventory.meta.PotionMeta) potion.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(ChatColor.LIGHT_PURPLE + itemName);
                    List<String> lore = new ArrayList<>();
                    lore.add(ChatColor.GRAY + "基础治疗药水，可以快速恢复生命值");
                    lore.add(ChatColor.GREEN + "效果: 瞬间治疗 I");
                    meta.setLore(lore);
                    // 设置药水颜色和效果
                    meta.setColor(org.bukkit.Color.RED);
                    meta.addCustomEffect(new PotionEffect(PotionEffectType.REGENERATION, 1, 0), true);
                    potion.setItemMeta(meta);
                }
                player.getInventory().addItem(potion);
                player.sendMessage(ChatColor.GREEN + "已获得: " + ChatColor.LIGHT_PURPLE + itemName);
                playSoundSafely(player, player.getLocation(), "ENTITY_ITEM_PICKUP", "ENTITY_ITEM_PICKUP", 1.0f, 1.0f);
            }
        } else {
            player.sendMessage(ChatColor.RED + "无效的道具ID！");
        }
    }

    /**
     * 激活指定ID的特殊功能
     *
     * @param itemId 特殊功能ID
     */
    public void activateSpecialFunction(String itemId) {
        if (buyConfig.contains("sp." + itemId)) {
            String functionName = buyConfig.getString("sp." + itemId + ".name", "未知功能");
            if (itemId.equalsIgnoreCase("id1")) { // 全体加速功能
                // 遍历所有在线玩家，给予速度buff2效果，持续15秒
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.SPEED, 15 * 20, 1, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "全体玩家已获得 " + ChatColor.AQUA + functionName + ChatColor.GOLD + " 效果，持续15秒！");
            } else if (itemId.equalsIgnoreCase("id2")) { // 全体回复功能(1)
                // 遍历所有在线玩家，给予瞬间治疗2效果
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.INSTANT_HEALTH, 1, 1, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "全体玩家已获得 " + ChatColor.AQUA + functionName + ChatColor.GOLD + " 效果！");
            } else if (itemId.equalsIgnoreCase("id3")) { // 全体回复功能(2)
                // 遍历所有在线玩家，给予生命回复2效果，持续10秒
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 10 * 20, 1, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "全体玩家已获得 " + ChatColor.AQUA + functionName + ChatColor.GOLD + " 效果，持续10秒！");
            } else if (itemId.equalsIgnoreCase("id4")) { // 大量牛奶功能
                // 给所有在线玩家一个牛奶
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.getInventory().addItem(new ItemStack(Material.MILK_BUCKET));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "所有玩家已获得一个牛奶！");
                // 显示特殊功能标题
                for (Player p : Bukkit.getOnlinePlayers()) {
                    sendTitle(p, 5, 20, 5, ChatColor.LIGHT_PURPLE + "大量牛奶！", ChatColor.YELLOW + "所有玩家获得了一个牛奶！");
                }
            } else if (itemId.equalsIgnoreCase("id5")) { // 全体回复(3)
                // 遍历所有在线玩家，给予生命回复2效果，持续10秒
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 10 * 20, 1, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "全体玩家已获得 " + ChatColor.AQUA + "全体回复(3)" + ChatColor.GOLD + " 效果，持续10秒！");

                // 显示特殊功能标题
                for (Player p : Bukkit.getOnlinePlayers()) {
                    sendTitle(p, 5, 20, 5, ChatColor.LIGHT_PURPLE + "全体回复(3)！", ChatColor.YELLOW + "所有玩家获得生命回复2效果！");
                }
            } // 新增：生命提升(1)
            else if (itemId.equalsIgnoreCase("id6")) { // 生命提升(1)
                // 遍历所有在线玩家，提升生命值
                for (Player p : Bukkit.getOnlinePlayers()) {
                    // 获取当前生命值
                    double currentHealth = p.getHealth();
                    // 设置新的生命值，最多不超过40（20 hearts）
                    double newHealth = Math.min(currentHealth + 8, 40.0);
                    p.setHealth(newHealth);
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "所有玩家的生命已提升！");
            } else if (itemId.equalsIgnoreCase("id7")) { // 生命提升(2)
                // 遍历所有在线玩家，给予伤害吸收buff5级效果，持续6分钟
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, 6 * 60 * 20, 4, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "所有玩家已获得 " + ChatColor.AQUA + "生命提升(2)" + ChatColor.GOLD + " 效果，持续6分钟！");

                // 显示特殊功能标题
                for (Player p : Bukkit.getOnlinePlayers()) {
                    sendTitle(p, 5, 20, 5, ChatColor.LIGHT_PURPLE + "生命提升(2)！", ChatColor.YELLOW + "所有玩家获得伤害吸收5级效果！");
                }
            } else if (itemId.equalsIgnoreCase("id8")) { // 力量
                // 遍历所有在线玩家，给予生命提升buff4级效果，持续时间无限
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.HEALTH_BOOST, Integer.MAX_VALUE, 3, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "所有玩家已获得 " + ChatColor.AQUA + "力量" + ChatColor.GOLD + " 效果，持续时间无限！");

                // 显示特殊功能标题
                for (Player p : Bukkit.getOnlinePlayers()) {
                    sendTitle(p, 5, 20, 5, ChatColor.LIGHT_PURPLE + "力量！", ChatColor.YELLOW + "所有玩家获得力量级效果！");
                }
            } else if (itemId.equalsIgnoreCase("id9")) { // 生命提升(3)
                // 遍历所有在线玩家，给予生命提升buff4级效果，持续时间无限
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.ABSORPTION, Integer.MAX_VALUE, 20, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "所有玩家已获得 " + ChatColor.AQUA + "生命提升(3)" + ChatColor.GOLD + " 效果，持续时间无限！");

                // 显示特殊功能标题
                for (Player p : Bukkit.getOnlinePlayers()) {
                    sendTitle(p, 5, 20, 5, ChatColor.LIGHT_PURPLE + "生命提升(3)！", ChatColor.YELLOW + "所有玩家获得生命提升4级效果！");
                }
            } else if (itemId.equalsIgnoreCase("id10")) { // 快速传送功能
                // 遍历所有在线玩家，给予一次传送机会
                for (Player p : Bukkit.getOnlinePlayers()) {
                    teleportCountMap.put(p, teleportCountMap.getOrDefault(p, 0) + 1);
                    p.sendMessage(ChatColor.GREEN + "你已获得一次 " + ChatColor.AQUA + "快速传送" + ChatColor.GREEN + " 的机会！");
                    // 发送标题
                    sendTitle(p, 5, 20, 5, ChatColor.LIGHT_PURPLE + "快速传送！", ChatColor.YELLOW + "你获得了一次快速传送的机会！");
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "所有玩家已获得 " + ChatColor.AQUA + "快速传送" + ChatColor.GOLD + " 的机会！");
            } else if (itemId.equalsIgnoreCase("id11")) { // 无敌时间
                // 遍历所有在线玩家，给予抗性提升buff19级效果，持续15秒
                for (Player p : Bukkit.getOnlinePlayers()) {
                    p.addPotionEffect(new PotionEffect(PotionEffectType.REGENERATION, 15 * 20, 18, false, false));
                }
                Bukkit.broadcastMessage(ChatColor.GOLD + "所有玩家已获得 " + ChatColor.AQUA + "无敌时间" + ChatColor.GOLD + " 效果，持续15秒！");

                // 显示特殊功能标题
                for (Player p : Bukkit.getOnlinePlayers()) {
                    sendTitle(p, 5, 20, 5, ChatColor.LIGHT_PURPLE + "无敌时间！", ChatColor.YELLOW + "所有玩家获得抗性提升19级效果！");
                }
            } else {
                Bukkit.getLogger().warning("未定义的特殊功能ID: " + itemId);
            }
        } else {
            Bukkit.getLogger().warning("无效的特殊功能ID: " + itemId);
        }
    }

    /**
     * 获取玩家的当前金钱
     *
     * @param player 目标玩家
     * @return 当前金钱数量
     */
    public double getPlayerMoney(Player player) {
        return playerDataConfig.getDouble("money." + player.getUniqueId().toString(), buyConfig.getDouble("default_money", 100.0));
    }

    /**
     * 监听实体伤害事件，防止 ArmorStand 被破坏
     *
     * @param event 实体伤害事件
     */
    @EventHandler
    public void onEntityDamage(EntityDamageEvent event) {
        Entity entity = event.getEntity();
        if (entity instanceof ArmorStand) {
            event.setCancelled(true); // 取消伤害事件
        }
    }



    /**
     * 监听玩家丢弃物品事件，防止玩家丢弃枪支
     * 实现正确的处理流程：按Q丢弃 → 物品数量-1 → 取消事件 → 物品数量+1 → 结束
     *
     * @param event 玩家丢弃物品事件
     */
    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerDropItem(PlayerDropItemEvent event) {
        Player player = event.getPlayer();
        ItemStack droppedItem = event.getItemDrop().getItemStack();

        // 检查是否为枪支物品
        if (isGunItem(droppedItem)) {
            // 记录当前主手物品的数量（此时已经-1了）
            ItemStack mainHandItem = player.getInventory().getItemInMainHand();
            int currentAmount = 0;
            if (mainHandItem != null && isGunItem(mainHandItem)) {
                currentAmount = mainHandItem.getAmount();
                getLogger().info("玩家 " + player.getName() + " 丢弃后当前枪支数量: " + currentAmount);
            }

            // 取消丢弃事件
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "不能丢弃枪支物品！");
            }

            // 执行+1恢复操作（真正的+1）
            if (mainHandItem != null && isGunItem(mainHandItem)) {
                int restoredAmount = currentAmount + 1;
                mainHandItem.setAmount(restoredAmount);
                player.getInventory().setItemInMainHand(mainHandItem);
                getLogger().info("恢复玩家 " + player.getName() + " 枪支数量: " + currentAmount + " -> " + restoredAmount);
            }

            // 延迟清理重复枪支，避免干扰恢复流程
            getServer().getScheduler().runTaskLater(this, () -> {
                cleanupDuplicateGuns(player);
            }, 2L);
        }
    }



    /**
     * 清理玩家物品栏中重复的枪支
     * 如果发现同类型的枪支，只保留一把，其他的自动清除
     *
     * @param player 玩家
     */
    private void cleanupDuplicateGuns(Player player) {
        Map<String, Integer> gunTypeCount = new HashMap<>();
        Map<String, List<Integer>> gunSlots = new HashMap<>();

        // 扫描玩家物品栏，统计每种枪支的数量和位置
        for (int i = 0; i < player.getInventory().getSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (item != null && isGunItem(item)) {
                String gunType = getGunTypeFromItem(item);
                if (gunType != null) {
                    gunTypeCount.put(gunType, gunTypeCount.getOrDefault(gunType, 0) + 1);
                    gunSlots.computeIfAbsent(gunType, k -> new ArrayList<>()).add(i);
                }
            }
        }

        // 清理重复的枪支
        for (Map.Entry<String, Integer> entry : gunTypeCount.entrySet()) {
            String gunType = entry.getKey();
            int count = entry.getValue();

            if (count > 1) {
                List<Integer> slots = gunSlots.get(gunType);
                // 保留第一把，清除其他的
                for (int i = 1; i < slots.size(); i++) {
                    int slot = slots.get(i);
                    player.getInventory().setItem(slot, null);
                    getLogger().info("自动清理了玩家 " + player.getName() + " 的重复枪支: " + gunType + " (槽位: " + slot + ")");
                }
            }
        }
    }

    /**
     * 从物品获取枪支类型
     *
     * @param item 物品
     * @return 枪支类型，如果不是枪支则返回null
     */
    private String getGunTypeFromItem(ItemStack item) {
        if (item == null || !isGunItem(item)) {
            return null;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return null;
        }

        String displayName = ChatColor.stripColor(meta.getDisplayName());

        // 检查是否是弹药耗尽的武器（钻石）
        if (item.getType() == Material.DIAMOND && displayName.contains("弹药耗尽的武器")) {
            // 从lore中获取原始枪支类型
            if (meta.hasLore()) {
                List<String> lore = meta.getLore();
                if (lore != null) {
                    for (String line : lore) {
                        if (line.contains("gun_id:")) {
                            return ChatColor.stripColor(line).replace("gun_id:", "").trim();
                        }
                    }
                }
            }
        }

        // 检查是否是正常的枪支
        ConfigurationSection gunsSection = config.getConfigurationSection("guns");
        if (gunsSection != null) {
            for (String gunId : gunsSection.getKeys(false)) {
                String gunName = config.getString("guns." + gunId + ".name", "未知武器");
                if (displayName.equals(gunName)) {
                    return gunId;
                }
            }
        }

        return null;
    }



    /**
     * 监听玩家交换主副手物品事件，防止玩家通过F键绕过枪支保护
     * 使用LOWEST优先级确保最早拦截
     *
     * @param event 玩家交换主副手物品事件
     */
    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerSwapHandItems(PlayerSwapHandItemsEvent event) {
        Player player = event.getPlayer();
        ItemStack mainHandItem = event.getMainHandItem();
        ItemStack offHandItem = event.getOffHandItem();

        // 检查主手或副手是否有枪支物品
        if ((mainHandItem != null && isGunItem(mainHandItem)) ||
            (offHandItem != null && isGunItem(offHandItem))) {
            // 取消交换事件
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "不能交换枪支物品到副手！");
            }
        }
    }

    /**
     * 设置玩家的金钱
     *
     * @param player 目标玩家
     * @param amount 金钱数量
     */
    public void setPlayerMoney(Player player, double amount) {
        playerDataConfig.set("money." + player.getUniqueId().toString(), amount);
        saveAllConfigs();
    }

    /**
     * 增加玩家的金钱
     *
     * @param player 目标玩家
     * @param amount 增加的金钱数量
     */
    public void addPlayerMoney(Player player, double amount) {
        double currentMoney = getPlayerMoney(player);
        setPlayerMoney(player, currentMoney + amount);
    }

    /**
     * 减少玩家的金钱
     *
     * @param player 目标玩家
     * @param amount 减少的金钱数量
     * @return 是否成功减少（余额是否足够）
     */
    public boolean subtractPlayerMoney(Player player, double amount) {
        double currentMoney = getPlayerMoney(player);
        if (currentMoney < amount) {
            return false;
        }
        setPlayerMoney(player, currentMoney - amount);
        return true;
    }

    /**
     * 根据输入的ID或中文名称获取武器ID
     *
     * @param input 输入的ID或中文名称
     * @return 武器ID，如果未找到则返回 null
     */
    private String getGunIdByInput(String input) {
        if (input == null || input.isEmpty()) {
            return null;
        }

        // 首先检查是否直接是ID
        if (config.contains("guns." + input)) {
            return input;
        }

        // 然后检查是否是中文名称
        ConfigurationSection gunsSection = config.getConfigurationSection("guns");
        if (gunsSection != null) {
            for (String gunId : gunsSection.getKeys(false)) {
                String gunName = config.getString("guns." + gunId + ".name", "");
                if (gunName.equals(input)) {
                    return gunId;
                }
            }
        }

        return null;
    }

    /**
     * 显示可用的武器列表
     *
     * @param player 玩家
     */
    private void showAvailableGuns(Player player) {
        ConfigurationSection gunsSection = config.getConfigurationSection("guns");
        if (gunsSection != null) {
            player.sendMessage(ChatColor.YELLOW + "可用武器列表:");
            for (String gunId : gunsSection.getKeys(false)) {
                String gunName = config.getString("guns." + gunId + ".name", "未知武器");
                player.sendMessage(ChatColor.GRAY + "- " + ChatColor.WHITE + gunId +
                                 ChatColor.GRAY + " (" + ChatColor.GOLD + gunName + ChatColor.GRAY + ")");
            }
        }
    }

    /**
     * 根据输入的类型名称获取类型代码
     *
     * @param input 输入的类型名称（中文或英文缩写）
     * @return 类型代码，如果未找到则返回 null
     */
    private String getTypeByInput(String input) {
        if (input == null || input.isEmpty()) {
            return null;
        }

        // 直接匹配英文缩写
        if (input.matches("ar|wp|it|sp")) {
            return input;
        }

        // 匹配中文名称
        switch (input) {
            case "护甲":
                return "ar";
            case "武器":
                return "wp";
            case "道具":
                return "it";
            case "特殊功能":
                return "sp";
            default:
                return null;
        }
    }

    /**
     * 根据输入的ID或中文名称获取物品ID
     *
     * @param input 输入的ID或中文名称
     * @param type 物品类型
     * @return 物品ID，如果未找到则返回 null
     */
    private String getBuyItemIdByInput(String input, String type) {
        if (input == null || input.isEmpty() || type == null) {
            return null;
        }

        // 首先检查是否直接是ID
        if (buyConfig.contains(type + "." + input)) {
            return input;
        }

        // 然后检查是否是中文名称
        ConfigurationSection typeSection = buyConfig.getConfigurationSection(type);
        if (typeSection != null) {
            for (String itemId : typeSection.getKeys(false)) {
                String itemName = buyConfig.getString(type + "." + itemId + ".name", "");
                if (itemName.equals(input)) {
                    return itemId;
                }
            }
        }

        return null;
    }

    /**
     * 显示指定类型的可用物品列表
     *
     * @param player 玩家
     * @param type 物品类型
     */
    private void showAvailableBuyItems(Player player, String type) {
        ConfigurationSection typeSection = buyConfig.getConfigurationSection(type);
        if (typeSection != null) {
            String typeName = getTypeDisplayName(type);
            player.sendMessage(ChatColor.YELLOW + "可用" + typeName + "列表:");
            for (String itemId : typeSection.getKeys(false)) {
                String itemName = buyConfig.getString(type + "." + itemId + ".name", "未知物品");
                int price = buyConfig.getInt(type + "." + itemId + ".price", 0);
                player.sendMessage(ChatColor.GRAY + "- " + ChatColor.WHITE + itemId +
                                 ChatColor.GRAY + " (" + ChatColor.GOLD + itemName +
                                 ChatColor.GRAY + " - " + ChatColor.GREEN + price + "金币)");
            }
        }
    }

    /**
     * 获取类型的中文显示名称
     *
     * @param type 类型代码
     * @return 中文显示名称
     */
    private String getTypeDisplayName(String type) {
        switch (type) {
            case "ar":
                return "护甲";
            case "wp":
                return "武器";
            case "it":
                return "道具";
            case "sp":
                return "特殊功能";
            default:
                return type;
        }
    }

    /**
     * 获取玩家当前手持武器的ID
     *
     * @param player 玩家
     * @return 武器ID，如果未找到则返回 null
     */
    String getPlayerGunId(Player player) {
        ItemStack item = player.getInventory().getItemInHand();
        if (item != null) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null && meta.hasDisplayName()) {
                String displayName = ChatColor.stripColor(meta.getDisplayName());

                // 检查是否是弹药耗尽的武器（钻石）
                if (item.getType() == Material.DIAMOND && displayName.contains("弹药耗尽的武器")) {
                    // 从lore中获取原始枪支ID
                    if (meta.hasLore()) {
                        List<String> lore = meta.getLore();
                        if (lore != null) {
                            for (String line : lore) {
                                if (line.contains("gun_id:")) {
                                    // 提取gun_id后面的内容
                                    return ChatColor.stripColor(line).replace("gun_id:", "").trim();
                                }
                            }
                        }
                    }
                }

                // 检查是否是正常的枪支
                for (String gunId : config.getConfigurationSection("guns").getKeys(false)) {
                    String gunName = config.getString("guns." + gunId + ".name", "未知武器");
                    if (displayName.equals(gunName)) {
                        return gunId;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 监听玩家交互事件，实现射击功能和双击左键检测
     *
     * @param event 玩家交互事件
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItemInMainHand();

        // 检查手持物品是否为武器
        if (isGunItem(item)) {
            ItemMeta meta = item.getItemMeta();
            String displayName = ChatColor.stripColor(meta.getDisplayName());
            String gunId = null;

            // 尝试从配置中找到匹配的武器ID
            ConfigurationSection gunsSection = config.getConfigurationSection("guns");
            if (gunsSection != null) {
                for (String id : gunsSection.getKeys(false)) {
                    String gunName = config.getString("guns." + id + ".name", "未知武器");
                    if (displayName.equals(gunName)) {
                        gunId = id;
                        break;
                    }
                }
            }

            if (gunId != null) {
                // 更新当前手持武器ID
                currentGunMap.put(player, gunId);

                // 初始化弹药数据结构
                if (!ammoMap.containsKey(player)) {
                    ammoMap.put(player, new HashMap<>());
                }

                Map<String, Integer> playerAmmo = ammoMap.get(player);

                // 初始化弹夹数据结构
                if (!clipAmmoMap.containsKey(player)) {
                    clipAmmoMap.put(player, new HashMap<>());
                }

                Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

                // 初始化枪支状态数据结构
                if (!gunEmptyStatusMap.containsKey(player)) {
                    gunEmptyStatusMap.put(player, new HashMap<>());
                }

                Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);

                // 检查枪支是否处于弹药耗尽状态
                if (playerGunStatus.getOrDefault(gunId, false)) {
                    // 如果枪支已经处于弹药耗尽状态，直接禁止射击
                    player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                    player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                            TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
                    event.setCancelled(true);
                    return;
                }

                // 获取弹夹中的子弹数
                int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

                // 获取总弹药数
                String gunIdKey = gunId + "_total";
                int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

                // 检查总弹药数和弹夹子弹数是否都为0
                if (totalAmmo <= 0 && currentClipAmmo <= 0) {
                    // 总弹药数和弹夹子弹数都为0，将枪支变成钻石

                    // 设置枪支状态为弹药耗尽
                    playerGunStatus.put(gunId, true);

                    // 获取枪支名称
                    String gunName = "";
                    if (isGunItem(item) && item.getItemMeta() != null) {
                        gunName = item.getItemMeta().getDisplayName();
                    }

                    // 创建一个钻石物品
                    ItemStack emptyGun = new ItemStack(Material.DIAMOND);
                    ItemMeta emptyMeta = emptyGun.getItemMeta();
                    if (emptyMeta != null) {
                        // 设置显示名称
                        emptyMeta.setDisplayName(ChatColor.RED + "弹药耗尽的武器");

                        // 设置Lore
                        List<String> lore = new ArrayList<>();
                        lore.add(ChatColor.GRAY + "原武器: " + ChatColor.YELLOW + gunName);
                        lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                        lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.RED + "0");
                        lore.add("");
                        lore.add(ChatColor.RED + "弹药已耗尽，无法射击");
                        lore.add(ChatColor.YELLOW + "请使用 /shoot up 补充弹药");

                        // 存储原始枪支ID，用于恢复
                        lore.add(ChatColor.BLACK + "gun_id:" + gunId);

                        emptyMeta.setLore(lore);
                        emptyGun.setItemMeta(emptyMeta);
                    }

                    // 替换玩家手中的枪支
                    player.getInventory().setItemInMainHand(emptyGun);

                    // 设置经验值为0，显示弹药耗尽状态
                    player.setLevel(0);
                    player.setExp(0);

                    player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                    player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                            TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));

                    // 取消事件
                    event.setCancelled(true);
                    return;
                }

                updatePlayerXP(player, gunId);

                // 检查玩家是否在换弹冷却中
                if (isReloading.contains(player.getUniqueId())) {
                    player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                            TextComponent.fromLegacyText(ChatColor.RED + "正在换弹中..."));
                    return;
                }

                // 检查玩家是否在射击冷却中
                boolean isCooldown = isCooldownMap.getOrDefault(player, new HashMap<>()).getOrDefault(gunId, false);
                if (isCooldown) {
                    player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                            TextComponent.fromLegacyText(ChatColor.RED + "武器冷却中..."));
                    return;
                }

                // 判断交互类型
                switch (event.getAction()) {
                    case RIGHT_CLICK_AIR:
                    case RIGHT_CLICK_BLOCK:
                        // 右键点击，尝试射击
                        event.setCancelled(true); // 防止方块交互
                        handleShooting(player, gunId);
                        break;

                    case LEFT_CLICK_AIR:
                    case LEFT_CLICK_BLOCK:
                        // 左键点击，检测双击
                        handleLeftClick(player);
                        break;

                    default:
                        break;
                }
            }
        } else if (item != null && item.getType() == Material.DIAMOND && item.getItemMeta() != null
                && item.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {
            // 如果玩家手持的是弹药耗尽的武器（钻石），取消交互事件
            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            event.setCancelled(true);
        }
    }

    /**
     * 监听玩家切换物品栏中的物品，更新经验值显示
     *
     * @param event 玩家切换物品栏事件
     */
    @EventHandler
    public void onPlayerItemHeld(PlayerItemHeldEvent event
    ) {
        Player player = event.getPlayer();
        ItemStack item = player.getInventory().getItem(event.getNewSlot());

        if (item != null) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null && meta.hasDisplayName()) {
                String displayName = ChatColor.stripColor(meta.getDisplayName());
                String gunId = null;

                // 检查是否是弹药耗尽的武器（钻石）
                if (item.getType() == Material.DIAMOND && displayName.contains("弹药耗尽的武器")) {
                    // 从lore中获取原始枪支ID
                    if (meta.hasLore()) {
                        List<String> lore = meta.getLore();
                        if (lore != null) {
                            for (String line : lore) {
                                if (line.contains("gun_id:")) {
                                    // 提取gun_id后面的内容
                                    gunId = ChatColor.stripColor(line).replace("gun_id:", "").trim();
                                    break;
                                }
                            }
                        }
                    }

                    if (gunId != null) {
                        // 更新当前手持武器ID
                        currentGunMap.put(player, gunId);
                        // 设置经验值为0，显示弹药耗尽状态
                        player.setLevel(0);
                        player.setExp(0);
                        return;
                    }
                }

                // 检查是否是正常的枪支
                for (String id : config.getConfigurationSection("guns").getKeys(false)) {
                    String gunName = config.getString("guns." + id + ".name", "未知武器");
                    if (displayName.equals(gunName)) {
                        gunId = id;
                        break;
                    }
                }

                if (gunId != null) {
                    // 更新当前手持武器ID
                    currentGunMap.put(player, gunId);
                    updatePlayerXP(player, gunId);
                } else {
                    // 如果不是武器，清空XP显示
                    player.setExp(0);
                    player.setLevel(0);
                    currentGunMap.remove(player);
                }
            } else {
                // 如果不是武器，清空XP显示
                player.setExp(0);
                player.setLevel(0);
                currentGunMap.remove(player);
            }
        } else {
            // 如果手持物品为空，清空XP显示
            player.setExp(0);
            player.setLevel(0);
            currentGunMap.remove(player);
        }
    }

    /**
     * 处理射击逻辑，包括弹药检查、射击计数、限制和冷却
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     */
    private void handleShooting(Player player, String gunId) {
        // 检查是否允许射击
        if (isReloading.contains(player.getUniqueId())) {
            return;
        }

        // 检查冷却
        if (isCooldownMap.containsKey(player) && isCooldownMap.get(player).containsKey(gunId)
                && isCooldownMap.get(player).get(gunId)) {
            return; // 玩家在冷却中，不能射击，但不显示提示
        }

        // 检查弹药
        ItemStack gunItem = player.getInventory().getItemInMainHand();
        if (!isGunItem(gunItem)) {
            return;
        }

        // 初始化弹药数据结构 - 确保总是创建新的Map实例
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());

            // 如果玩家没有弹药数据，说明这是新获得的武器，应该有满弹药
            // 获取最大弹药量和弹夹容量
            int maxAmmo = config.getInt("guns." + gunId + ".ammo", 300);
            int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);

            // 设置初始总弹药量
            ammoMap.get(player).put(gunId + "_total", maxAmmo);

            // 初始化弹夹数据结构
            if (!clipAmmoMap.containsKey(player)) {
                clipAmmoMap.put(player, new HashMap<>());
            }

            // 设置初始弹夹子弹数
            clipAmmoMap.get(player).put(gunId, clipSize);

            // 打印调试信息
            getLogger().info("为玩家 " + player.getName() + " 初始化武器 " + gunId + " 的弹药: 弹夹=" + clipSize + ", 总弹药=" + maxAmmo);
        }
        Map<String, Integer> playerAmmo = ammoMap.get(player);

        // 初始化枪支状态数据结构
        if (!gunEmptyStatusMap.containsKey(player)) {
            gunEmptyStatusMap.put(player, new HashMap<>());
        }
        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);

        // 检查枪支是否处于弹药耗尽状态
        if (playerGunStatus.getOrDefault(gunId, false)) {
            // 如果枪支已经处于弹药耗尽状态，直接禁止射击
            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return;
        }

        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());

            // 如果玩家没有弹夹数据，说明这是新获得的武器，应该有满弹夹
            int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);
            clipAmmoMap.get(player).put(gunId, clipSize);

            // 打印调试信息
            getLogger().info("为玩家 " + player.getName() + " 初始化武器 " + gunId + " 的弹夹: 弹夹=" + clipSize);
        }
        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

        // 获取弹夹中的子弹数 - 如果没有数据，使用最大弹夹容量作为默认值
        int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);
        int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, clipSize);

        // 获取总弹药数 - 如果没有数据，使用最大弹药量作为默认值
        String gunIdKey = gunId + "_total";
        int maxAmmo = config.getInt("guns." + gunId + ".ammo", 300);
        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, maxAmmo);

        // 检查总弹药数和弹夹子弹数是否都为0
        if (totalAmmo <= 0 && currentClipAmmo <= 0) {
            // 总弹药数和弹夹子弹数都为0，显示弹药耗尽提示

            // 设置枪支状态为弹药耗尽
            playerGunStatus.put(gunId, true);

            // 获取枪支名称
            String gunName = "";
            if (isGunItem(gunItem) && gunItem.getItemMeta() != null) {
                gunName = gunItem.getItemMeta().getDisplayName();
            }

            // 创建一个钻石物品
            ItemStack emptyGun = new ItemStack(Material.DIAMOND);
            ItemMeta emptyMeta = emptyGun.getItemMeta();
            if (emptyMeta != null) {
                // 设置显示名称
                emptyMeta.setDisplayName(ChatColor.RED + "弹药耗尽的武器");

                // 设置Lore
                List<String> lore = new ArrayList<>();
                lore.add(ChatColor.GRAY + "原武器: " + ChatColor.YELLOW + gunName);
                lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.RED + "0");
                lore.add("");
                lore.add(ChatColor.RED + "弹药已耗尽，无法射击");
                lore.add(ChatColor.YELLOW + "请使用 /shoot up 补充弹药");

                // 存储原始枪支ID，用于恢复
                lore.add(ChatColor.BLACK + "gun_id:" + gunId);

                emptyMeta.setLore(lore);
                emptyGun.setItemMeta(emptyMeta);
            }

            // 替换玩家手中的枪支
            player.getInventory().setItemInMainHand(emptyGun);

            // 设置经验值为0，显示弹药耗尽状态
            player.setLevel(0);
            player.setExp(0);

            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return; // 如果总弹药数和弹夹子弹数都为0，不允许开枪
        }

        // 检查弹夹中的子弹数
        if (currentClipAmmo <= 0) {
            // 弹夹中弹药不足，需要换弹
            reloadGun(player, gunId);
            return;
        }

        // 物品数量显示当前弹夹中的子弹数
        gunItem.setAmount(currentClipAmmo);

        // 启动冷却
        int cooldownSeconds = config.getInt("guns." + gunId + ".cooldown", 1);
        startCooldown(player, gunId, cooldownSeconds);

        // 获取粒子类型（从配置文件读取）
        String particleTypeName = config.getString("guns." + gunId + ".particle", "CRIT");

        // 确保玩家有显示设置，如果没有则从DeathZombieV4同步
        initializePlayerDisplaySettings(player);

        // 获取DisplaySettings对象
        DisplaySettings settings = displaySettingsMap.get(player);

        // 根据武器类型选择不同的处理方法
        switch (gunId) {
            case "id1": // 手枪 - 单发暴击粒子
                handleSingleBullet(player, gunId, settings);
                break;
            case "id2": // 步枪 - 白色粒子直线
                handleSingleBullet(player, gunId, settings);
                break;
            case "id3": // 霰弹枪 - 多发散射
                handleMultipleBullets(player, gunId, settings);
                break;
            case "id4": // 机枪 - 暴击粒子
                handleMultipleBullets(player, gunId, settings);
                break;
            case "id5": // 火箭筒
                handleRocketLauncher(player, gunId, settings);
                break;
            case "id6": // 电击枪
                handleElectricGun(player, gunId, settings);
                break;
            case "id7": // 狙击步枪
                handleSniperRifle(player, gunId, settings);
                break;
            case "id8": // 冷冻枪
                handleFreezingGun(player, gunId, settings);
                break;
            case "id9": // 雷击枪
                handleLightningGun(player, gunId, settings);
                break;
            case "id10": // 压强枪
                handlePressureGun(player, gunId, settings);
                break;
            case "id11": // 突击步枪
                handleAssaultRifle(player, gunId, settings);
                break;
            case "id12": // 冲锋枪
                handleSubmachineGun(player, gunId, settings);
                break;
            case "id13": // 等离子枪
                handlePlasmaGun(player, gunId, settings);
                break;
            case "id14": // 死神收割者
                handleReaperGun(player, gunId, settings);
                break;
            case "id15": // 毁灭者
                handleDestroyerGun(player, gunId, settings);
                break;
            case "id16": // 超级激光炮
                handleSuperLaserGun(player, gunId, settings);
                break;
            case "id17": // 黑洞吞噬者
                handleBlackHoleGun(player, gunId, settings);
                break;
            case "id18": // 音波步枪
                handleSonicRifle(player, gunId, settings);
                break;
            case "id19": // 能量脉冲枪
                handleEnergyPulseGun(player, gunId, settings);
                break;
            case "id20": // 彩虹喷射器
                handleRainbowGun(player, gunId, settings);
                break;
            case "id21": // 传送枪
                handleTeleportGun(player, gunId, settings);
                break;
            case "id22": // 星辉主宰者
                handleStarlightDominatorGun(player, gunId, settings);
                break;
            case "id23": // 虚空星尘使者
                handleVoidStardustGun(player, gunId, player.getTargetBlock((Set<Material>) null, 100).getLocation());
                break;
            case "id24": // 循声炮
                handleSonicCannon(player, gunId, settings);
                break;
            case "id25": // 火焰喷射器
                handleFlamethrower(player, gunId, settings);
                break;
            default:
                handleSingleBullet(player, gunId, settings);
                break;
        }

        // 播放射击音效
        String shootSound = config.getString("guns." + gunId + ".shoot_sound_start", "");
        if (!shootSound.isEmpty()) {
            playSoundSafely(player, player.getLocation(), shootSound, "ENTITY_ARROW_SHOOT", 1.0f, 1.0f);
        }
    }

    /**
     * 处理压强枪的射击效果
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    private void handlePressureGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone(); // 子弹起始位置
            int distance = 0; // 子弹飞行距离
            double maxDistance = 30; // 最大射程

            @Override
            public void run() {
                if (distance > maxDistance) {
                    // 达到最大距离，取消任务
                    cancel();
                    return;
                }

                // 计算子弹前进的位置
                loc = loc.add(loc.getDirection().multiply(1));
                distance++;

                // 发送烟雾球体粒子效果
                particleHelper.displaySphere(loc, 1.5f, 20, Particle.CLOUD);

                // 检测子弹是否击中生物
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 对被击中的生物造成子弹伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 8);
                        target.damage(damage, player);

                        // 播放击中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_GENERIC_EXPLODE", 1.0f, 1.0f);
                        }

                        // 显示击中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.RED + "击中！", ChatColor.YELLOW + "烟雾击中目标！");
                        }

                        // 特殊效果：击退目标并造成额外伤害
                        double specialDamage = config.getDouble("guns." + gunId + ".special_damage", 15);
                        target.damage(specialDamage, player);
                        target.setVelocity(target.getLocation().getDirection().multiply(-2)); // 击退目标

                        // 取消子弹飞行
                        cancel();
                        return;
                    }
                }

                distance++;
            }
        }.runTaskTimer(this, 0, 1); // 每1 tick更新一次
    }

    /**
     * 处理手枪和步枪的单发子弹射击效果
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    private void handleSingleBullet(Player player, String gunId, DisplaySettings settings) {
        // 检查是否有足够的弹药
        ItemStack gunItem = player.getInventory().getItemInMainHand();

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }

        // 初始化枪支状态数据结构
        if (!gunEmptyStatusMap.containsKey(player)) {
            gunEmptyStatusMap.put(player, new HashMap<>());
        }

        // 检查枪支是否处于弹药耗尽状态
        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);
        if (playerGunStatus.getOrDefault(gunId, false)) {
            // 如果枪支已经处于弹药耗尽状态，直接禁止射击
            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return;
        }

        // 获取总弹药数
        String gunIdKey = gunId + "_total";
        Map<String, Integer> playerAmmo = ammoMap.get(player);
        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

        // 获取弹夹中的子弹数
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());
        }
        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);
        int clipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

        // 修复bug：只有当弹夹中的子弹数为0且总子弹数为0时，才将枪支状态设置为弹药耗尽
        if (totalAmmo <= 0 && clipAmmo <= 0) {
            // 设置枪支状态为弹药耗尽
            playerGunStatus.put(gunId, true);

            // 获取枪支名称
            String gunName = "";
            if (isGunItem(gunItem) && gunItem.getItemMeta() != null) {
                gunName = gunItem.getItemMeta().getDisplayName();
            }

            // 创建一个钻石物品
            ItemStack emptyGun = new ItemStack(Material.DIAMOND);
            ItemMeta emptyMeta = emptyGun.getItemMeta();
            if (emptyMeta != null) {
                // 设置显示名称
                emptyMeta.setDisplayName(ChatColor.RED + "弹药耗尽的武器");

                // 设置Lore
                List<String> lore = new ArrayList<>();
                lore.add(ChatColor.GRAY + "原武器: " + ChatColor.YELLOW + gunName);
                lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.RED + "0");
                lore.add("");
                lore.add(ChatColor.RED + "弹药已耗尽，无法射击");
                lore.add(ChatColor.YELLOW + "请使用 /shoot up 补充弹药");

                // 存储原始枪支ID，用于恢复
                lore.add(ChatColor.BLACK + "gun_id:" + gunId);

                emptyMeta.setLore(lore);
                emptyGun.setItemMeta(emptyMeta);
            }

            // 替换玩家手中的枪支
            player.getInventory().setItemInMainHand(emptyGun);

            // 设置经验值为0，显示弹药耗尽状态
            player.setLevel(0);
            player.setExp(0);

            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return;
        } else if (totalAmmo <= 0 && clipAmmo > 0) {
            // 如果总弹药为0但弹夹中还有子弹，显示提示但允许继续射击
            player.sendMessage(ChatColor.YELLOW + "总弹药已耗尽，但弹夹中还有 " + clipAmmo + " 发子弹！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.YELLOW + "总弹药已耗尽，弹夹中还有 " + clipAmmo + " 发子弹！"));
        }

        // 检查是否需要换弹
        if (!isGunItem(gunItem) || gunItem.getAmount() < 1) {
            // 检查是否有总弹药可以补充
            if (totalAmmo > 0) {
                reloadGun(player, gunId);
            }
            return;
        }

        // 使用已经获取的总弹药数量
        // 如果总弹药数小于1，检查弹夹中是否还有子弹
        if (totalAmmo < 1) {
            // 使用已经初始化的弹夹数据结构和变量
            if (clipAmmo > 0) {
                // 如果弹夹中还有子弹，显示总弹药耗尽但可以继续射击的提示
                player.sendMessage(ChatColor.YELLOW + "总弹药已耗尽，但弹夹中还有 " + clipAmmo + " 发子弹！");
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.YELLOW + "总弹药已耗尽，弹夹中还有 " + clipAmmo + " 发子弹！"));

                // 更新物品描述中的总弹药显示为耗尽
                if (isGunItem(gunItem)) {
                    ItemMeta meta = gunItem.getItemMeta();
                    if (meta != null && meta.hasLore()) {
                        List<String> lore = meta.getLore();
                        if (lore != null) {
                            for (int i = 0; i < lore.size(); i++) {
                                String line = lore.get(i);
                                if (line.contains("总弹药")) {
                                    lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                                }
                            }
                            meta.setLore(lore);
                            gunItem.setItemMeta(meta);
                        }
                    }
                }
            } else {
                // 如果弹夹中也没有子弹，显示弹药完全耗尽的提示
                player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
                return;
            }
        }

        // 不再减少总弹药数，只在换弹时减少
        // 直接使用经验值显示总弹药数
        player.setLevel(totalAmmo);

        // 如果总弹药数为0，更新经验条显示
        if (totalAmmo <= 0) {
            player.setExp(0);
        }

        // 使用已经初始化的弹夹数据结构
        int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

        // 减少弹夹中的子弹数
        currentClipAmmo--;
        playerClipAmmo.put(gunId, currentClipAmmo);

        // 物品数量显示当前弹夹中的子弹数，但确保至少为1，防止物品消失
        if (currentClipAmmo <= 0) {
            // 如果弹夹中的子弹数为0或负数，设置物品数量为1，防止物品消失
            gunItem.setAmount(1);
        } else {
            // 否则，设置物品数量为当前弹夹中的子弹数
            gunItem.setAmount(currentClipAmmo);
        }
        player.getInventory()
                .setItemInMainHand(gunItem);

        // 更新物品描述中的当前弹药数量和总弹药数量显示
        ItemMeta meta = gunItem.getItemMeta();
        if (meta != null && meta.hasLore()) {
            List<String> lore = meta.getLore();
            if (lore != null) {
                for (int i = 0; i < lore.size(); i++) {
                    String line = lore.get(i);
                    if (line.contains("当前弹药")) {
                        lore.set(i, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + (gunItem.getAmount()));
                    } else if (line.contains("总弹药")) {
                        lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + totalAmmo);
                    }
                }
                meta.setLore(lore);
                gunItem.setItemMeta(meta);
            }
        }

        // 获取配置的粒子类型
        String particleType = config.getString("guns." + gunId + ".particle", "CRIT");

        // 创建子弹轨迹
        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 50;

            @Override
            public void run() {
                if (distance >= maxDistance) {
                    cancel();
                    return;
                }

                // 移动子弹位置
                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                // 根据不同武器ID选择粒子效果
                switch (gunId) {
                    case "id1": // 手枪 - 暴击粒子
                        particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.CRIT);
                        break;
                    case "id2": // 步枪 - 白色粒子
                        // 使用白色粒子形成直线
                        loc.getWorld().spawnParticle(
                                Particle.DUST,
                                loc,
                                2,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(org.bukkit.Color.WHITE, 1.0f)
                        );
                        break;
                    case "id3": // 冲锋枪 - 红色粒子
                        // 使用DustOptions创建红色粒子
                        loc.getWorld().spawnParticle(
                                Particle.DUST,
                                loc,
                                2,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(org.bukkit.Color.RED, 1.0f)
                        );
                        break;
                    case "id4": // 机枪 - 暴击粒子
                        particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.CRIT);
                        break;
                    case "id7": // 狙击步枪 - 爱心粒子
                        particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.HEART);
                        maxDistance = 200; // 狙击枪射程更长
                        break;
                    case "id8": // 冷冻枪 - 蓝色+烟雾粒子
                        // 使用DustOptions创建蓝色粒子
                        loc.getWorld().spawnParticle(
                                Particle.DUST,
                                loc,
                                2,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(org.bukkit.Color.BLUE, 1.0f)
                        );
                        // 添加烟雾效果
                        particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 2, Particle.SMOKE);
                        break;
                    case "id9": // 雷击枪 - 绿色暴击粒子
                        // 使用DustOptions创建绿色粒子
                        loc.getWorld().spawnParticle(
                                Particle.DUST,
                                loc,
                                2,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(org.bukkit.Color.GREEN, 1.0f)
                        );
                        break;
                    default: // 其他情况，使用配置的粒子类型
                        try {
                            particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.valueOf(particleType));
                        } catch (IllegalArgumentException e) {
                            particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.CRIT);
                        }
                        break;
                }

                // 检测碰撞
                if (!loc.getBlock().isPassable()) {
                    // 显示方块破碎效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.BLOCK);
                    cancel();
                    return;
                }

                // 检测是否击中实体
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.5, 0.5, 0.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 获取武器伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 5.0);

                        // 造成伤害
                        target.damage(damage, player);

                        // 命中提示
                        if (settings.isHitTitle() && entity instanceof Player) {
                            Player targetPlayer = (Player) entity;
                            sendTitle(targetPlayer, 5, 10, 5, "§c你被射击了!", "§e伤害: " + damage);
                        }

                        // 应用特殊效果
                        applySpecialEffects(gunId, player, target, loc);

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty() && entity instanceof Player) {
                            playSoundSafely((Player) entity, entity.getLocation(), hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.0f);
                        }

                        // 显示命中特效
                        particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0.2f, 15, Particle.CRIT);

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    // 应用特殊武器效果
    private void applySpecialEffects(String gunId, Player shooter, LivingEntity target, Location hitLoc) {
        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "");
        if (specialDamageStr.isEmpty()) {
            return;
        }

        double specialDamage = 0;
        try {
            specialDamage = Double.parseDouble(specialDamageStr);
        } catch (NumberFormatException e) {
            return;
        }

        switch (gunId) {
            case "id5": // 火箭筒
                // 爆炸效果
                target.getWorld().createExplosion(hitLoc, 0F, false, false, shooter);
                target.damage(specialDamage, shooter);
                break;

            case "id6": // 电击枪
                // 范围电击效果
                for (Entity nearby : target.getNearbyEntities(10, 10, 10)) {
                    if (nearby instanceof LivingEntity && nearby != shooter) {
                        // 创建电击效果
                        Location start = target.getLocation().add(0, 1, 0);
                        Location end = nearby.getLocation().add(0, 1, 0);

                        // 使用粒子助手创建电击线效果
                        particleHelper.displayLine(start, end, Particle.WITCH);

                        // 造成伤害
                        ((LivingEntity) nearby).damage(specialDamage, shooter);
                    }
                }
                break;

            case "id8": // 冷冻枪
                // 冰冻效果
                if (target instanceof LivingEntity) {
                    ((LivingEntity) target).addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 255)); // 2秒冰冻
                    target.damage(specialDamage, shooter);
                }
                break;

            case "id9": // 雷击枪
                // 闪电效果
                target.getWorld().strikeLightningEffect(target.getLocation());
                target.damage(specialDamage, shooter);

                for (Entity nearby : target.getNearbyEntities(10, 10, 10)) {
                    if (nearby instanceof LivingEntity && nearby != shooter && nearby != target) {
                        nearby.getWorld().strikeLightningEffect(nearby.getLocation());
                        ((LivingEntity) nearby).damage(specialDamage, shooter);
                    }
                }
                break;

            case "id10": // 压强枪
                // 击退效果
                org.bukkit.util.Vector knockback = shooter.getLocation().getDirection().normalize().multiply(6);
                target.setVelocity(knockback);
                target.damage(specialDamage, shooter);
                break;
        }
    }

    /**
     * 处理霰弹枪和机枪的多发子弹射击效果
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    // 已删除重复的方法
    /**
     * 处理火箭筒的射击效果，发射真实的烈焰弹并处理爆炸特殊伤害
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    /**
     * 处理通用的子弹逻辑，包括弹药检查、减少弹药数量等
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @return 如果可以射击返回true，否则返回false
     */
    private boolean handleCommonBulletLogic(Player player, String gunId) {
        // 检查是否有足够的弹药
        ItemStack gunItem = player.getInventory().getItemInMainHand();

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }

        // 初始化枪支状态数据结构
        if (!gunEmptyStatusMap.containsKey(player)) {
            gunEmptyStatusMap.put(player, new HashMap<>());
        }

        // 检查枪支是否处于弹药耗尽状态
        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);
        if (playerGunStatus.getOrDefault(gunId, false)) {
            // 如果枪支已经处于弹药耗尽状态，直接禁止射击
            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return false;
        }

        // 获取总弹药数
        String gunIdKey = gunId + "_total";
        Map<String, Integer> playerAmmo = ammoMap.get(player);
        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

        // 使用新方法判断武器是否真正耗尽子弹（同时考虑弹夹中的子弹和总子弹）
        if (isWeaponTrulyEmpty(player, gunId)) {
            // 设置枪支状态为弹药耗尽
            playerGunStatus.put(gunId, true);

            // 获取枪支名称
            String gunName = "";
            if (isGunItem(gunItem) && gunItem.getItemMeta() != null) {
                gunName = gunItem.getItemMeta().getDisplayName();
            }

            // 创建一个钻石物品
            ItemStack emptyGun = new ItemStack(Material.DIAMOND);
            ItemMeta emptyMeta = emptyGun.getItemMeta();
            if (emptyMeta != null) {
                // 设置显示名称
                emptyMeta.setDisplayName(ChatColor.RED + "弹药耗尽的武器");

                // 设置Lore
                List<String> lore = new ArrayList<>();
                lore.add(ChatColor.GRAY + "原武器: " + ChatColor.YELLOW + gunName);
                lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.RED + "0");
                lore.add("");
                lore.add(ChatColor.RED + "弹药已耗尽，无法射击");
                lore.add(ChatColor.YELLOW + "请使用 /shoot up 补充弹药");

                // 存储原始枪支ID，用于恢复
                lore.add(ChatColor.BLACK + "gun_id:" + gunId);

                emptyMeta.setLore(lore);
                emptyGun.setItemMeta(emptyMeta);
            }

            // 替换玩家手中的枪支
            player.getInventory().setItemInMainHand(emptyGun);

            // 设置经验值为0，显示弹药耗尽状态
            player.setLevel(0);
            player.setExp(0);

            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return false;
        }

        // 检查是否需要换弹
        if (!isGunItem(gunItem) || gunItem.getAmount() < 1) {
            // 检查是否有总弹药可以补充
            if (totalAmmo > 0) {
                reloadGun(player, gunId);
            }
            return false;
        }

        // 使用新方法判断武器是否真正耗尽子弹（同时考虑弹夹中的子弹和总子弹）
        // 这里不再仅仅检查总弹药数，而是同时考虑弹夹中的子弹
        if (isWeaponTrulyEmpty(player, gunId)) {
            // 设置枪进入子弹耗尽状态
            if (isGunItem(gunItem)) {
                ItemMeta meta = gunItem.getItemMeta();
                if (meta != null && meta.hasLore()) {
                    List<String> lore = meta.getLore();
                    if (lore != null) {
                        for (int i = 0; i < lore.size(); i++) {
                            String line = lore.get(i);
                            if (line.contains("总弹药")) {
                                lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                            }
                        }
                        meta.setLore(lore);
                        gunItem.setItemMeta(meta);
                    }
                }
            }
            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return false;
        }

        // 方案二：开枪时不减少总弹药数，只减少弹夹中的子弹数
        // 使用updatePlayerXP方法更新经验值显示
        updatePlayerXP(player, gunId);

        // 如果总弹药数为0，但弹夹中还有子弹，显示总弹药耗尽提示
        if (totalAmmo <= 0) {
            // 获取弹夹中的子弹数
            Map<String, Integer> clipAmmoMap = this.clipAmmoMap.getOrDefault(player, new HashMap<>());
            int clipAmmo = clipAmmoMap.getOrDefault(gunId, 0);
            if (clipAmmo > 0) {
                // 如果弹夹中还有子弹，显示总弹药耗尽但可以继续射击的提示
                player.sendMessage(ChatColor.YELLOW + "总弹药已耗尽，但弹夹中还有 " + clipAmmo + " 发子弹！");
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.YELLOW + "总弹药已耗尽，弹夹中还有 " + clipAmmo + " 发子弹！"));
            } else {
                // 如果弹夹中也没有子弹，显示弹药完全耗尽的提示
                player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            }
        }

        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());
        }
        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);
        int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

        // 减少弹夹中的子弹数
        currentClipAmmo--;
        playerClipAmmo.put(gunId, currentClipAmmo);

        // 物品数量显示当前弹夹中的子弹数，但确保至少为1，防止物品消失
        if (currentClipAmmo <= 0) {
            // 如果弹夹中的子弹数为0或负数，设置物品数量为1，防止物品消失
            gunItem.setAmount(1);
        } else {
            // 否则，设置物品数量为当前弹夹中的子弹数
            gunItem.setAmount(currentClipAmmo);
        }
        player.getInventory().setItemInMainHand(gunItem);

        // 更新物品描述中的当前弹药数量和总弹药数量显示
        ItemMeta meta = gunItem.getItemMeta();
        if (meta != null && meta.hasLore()) {
            List<String> lore = meta.getLore();
            if (lore != null) {
                for (int i = 0; i < lore.size(); i++) {
                    String line = lore.get(i);
                    if (line.contains("当前弹药")) {
                        lore.set(i, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + (gunItem.getAmount()));
                    } else if (line.contains("总弹药")) {
                        lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + totalAmmo);
                    }
                }
                meta.setLore(lore);
                gunItem.setItemMeta(meta);
            }
        }

        return true;
    }

    private void handleRocketLauncher(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        Location eyeLoc = player.getEyeLocation();
        // 创建一个Fireball实体
        Fireball fireball = player.launchProjectile(Fireball.class);
        fireball.setIsIncendiary(false); // 不点燃周围
        fireball.setYield(3.0f); // 爆炸范围增大

        // 将Fireball与玩家关联
        fireballMap.put(fireball, player);

        // 启动火箭弹轨迹的粒子效果（火焰圆圈）
        new BukkitRunnable() {
            Location loc = fireball.getLocation().clone();
            double radius = 1.5; // 火焰圆圈半径
            double angle = 0; // 当前角度
            double angularSpeed = Math.toRadians(10); // 角速度

            @Override
            public void run() {
                if (fireball.isDead() || !fireball.isValid()) {
                    // 火箭弹已死亡或无效，取消任务
                    cancel();
                    return;
                }

                // 获取火箭弹当前位置
                loc = fireball.getLocation().clone();

                // 发送火焰圆圈粒子效果
                for (int i = 0; i < 8; i++) {
                    angle += Math.PI / 4;
                    double x = radius * Math.cos(angle);
                    double z = radius * Math.sin(angle);
                    Location particleLoc = loc.clone().add(x, 0, z);
                    particleHelper.displayParticle(particleLoc, 0, 0, 0, 0, 1, Particle.FLAME);
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    /**
     * 处理电击枪的射击效果，发射药水效果粒子，并在命中后对周围生物造成特殊伤害
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    private void handleElectricGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        int rayCount = 5; // 电击枪发射5条电流
        double spreadAngle = 15; // 电流扩散角度

        for (int i = 0; i < rayCount; i++) {
            // 计算每条电流的方向
            double angle = Math.random() * spreadAngle - spreadAngle / 2;
            Location loc = player.getEyeLocation().clone();
            loc.setDirection(rotateVectorAroundY(loc.getDirection().clone(), Math.toRadians(angle)));

            // 启动电流轨迹任务
            new BukkitRunnable() {
                Location bulletLoc = loc.clone();
                int distance = 0; // 电流飞行距离
                double maxDistance = 50; // 最大射程

                @Override
                public void run() {
                    if (distance > maxDistance) {
                        // 达到最大距离，取消任务
                        cancel();
                        return;
                    }
                    // 计算电流前进的位置
                    bulletLoc = bulletLoc.add(bulletLoc.getDirection().multiply(1));

                    // 药水效果粒子
                    particleHelper.displayParticle(bulletLoc, 0, 0, 0, 0, 2, Particle.WITCH);

                    // 检测电流是否击中生物
                    for (Entity entity : bulletLoc.getWorld().getNearbyEntities(bulletLoc, 0.5, 0.5, 0.5)) {
                        if (entity instanceof LivingEntity && entity != player) {
                            // 对被击中的生物造成子弹伤害
                            double damage = config.getDouble("guns." + gunId + ".damage", 5);
                            ((LivingEntity) entity).damage(damage, player);

                            // 播放击中粒子效果
                            sendExplosionParticle(bulletLoc);

                            // 播放击中音效
                            String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                            if (!hitSound.isEmpty()) {
                                playSoundSafely(player, bulletLoc, hitSound, "ENTITY_GENERIC_EXPLODE", 1.0f, 1.0f);
                            }

                            // 显示击中提示
                            if (settings.isHitTitle()) {
                                sendTitle(player, 5, 20, 5, ChatColor.RED + "击中！", ChatColor.YELLOW + "你击中了一个目标！");
                            }

                            // 进行区域内特殊伤害
                            double specialDamage = config.getDouble("guns." + gunId + ".special_damage", 10);
                            Location impactLoc = bulletLoc.clone();
                            for (Entity nearbyEntity : impactLoc.getWorld().getNearbyEntities(impactLoc, 5, 5, 5)) { // 10x10区域，半径为5
                                if (nearbyEntity instanceof LivingEntity && nearbyEntity != entity && nearbyEntity != player) {
                                    ((LivingEntity) nearbyEntity).damage(specialDamage, player);
                                }
                            }

                            // 取消子弹飞行
                            cancel();
                            return;
                        }
                    }
                    distance++;
                }
            }.runTaskTimer(this, 0, 1); // 每1 tick更新一次
        }
    }

    /**
     * 发送云粒子效果
     *
     * @param particleLoc 粒子位置
     */
    private void sendCloudParticle(Location particleLoc) {
        // 使用particleHelper替代原来的ParticleEffect
        particleHelper.displayParticle(Bukkit.getOnlinePlayers(), particleLoc, 0, 0, 0, 0, 1, Particle.CLOUD);
    }

    /**
     * 发送爆炸粒子效果
     *
     * @param loc 粒子位置
     */
    private void sendExplosionParticle(Location loc) {
        // 使用particleHelper替代原来的ParticleEffect
        particleHelper.displayHugeExplosionParticle(Bukkit.getOnlinePlayers(), loc, 0, 0, 0, 0, 1);
    }

    /**
     * 发送火焰粒子效果
     *
     * @param loc 粒子位置
     */
    private void sendFlameParticle(Location loc) {
        // 使用particleHelper替代原来的ParticleEffect
        particleHelper.displayFlameParticle(Bukkit.getOnlinePlayers(), loc, 0, 0, 0, 0, 1);
    }

    /**
     * 通用方法：显示粒子效果
     */
    private void sendParticleEffect(Location loc, Particle particleType) {
        // 直接使用particleHelper
        particleHelper.displayParticle(Bukkit.getOnlinePlayers(), loc, 0, 0, 0, 0, 1, particleType);
    }

    /**
     * 处理狙击步枪的射击效果
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    private void handleSniperRifle(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone(); // 子弹起始位置
            int distance = 0; // 子弹飞行距离
            double maxDistance = 200; // 超长射程

            @Override
            public void run() {
                if (distance > maxDistance) {
                    // 达到最大距离，取消任务
                    cancel();
                    return;
                }
                // 计算子弹前进的位置
                loc = loc.add(loc.getDirection().multiply(2));

                // 发送粒子效果
                particleHelper.displayParticle(Bukkit.getOnlinePlayers(), loc, 0, 0, 0, 0, 1, Particle.CRIT);

                // 检测子弹是否击中生物
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.5, 0.5, 0.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        // 对被击中的生物造成子弹伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 20);
                        ((LivingEntity) entity).damage(damage, player);

                        // 显示爆炸粒子效果
                        particleHelper.displayExplosion(loc, Particle.EXPLOSION_EMITTER, 20);

                        // 播放击中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_GENERIC_EXPLODE", 1.0f, 1.0f);
                        }

                        // 显示击中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.RED + "击中！", ChatColor.YELLOW + "你击中了一个目标！");
                        }

                        // 取消子弹飞行
                        cancel();
                        return;
                    }
                }
                distance += 2; // 增加飞行距离
            }
        }.runTaskTimer(this, 0, 1); // 每1 tick更新一次
    }

    /**
     * 处理冷冻枪的射击效果，发射蓝色+烟雾粒子，并在命中后冻结目标
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    private void handleFreezingGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone(); // 子弹起始位置
            int distance = 0; // 子弹飞行距离
            double maxDistance = 50; // 最大射程
            long freezeDurationTicks = 40; // 冻结持续时间（2秒）

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                // 移动子弹
                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 蓝色粒子 + 烟雾效果
                loc.getWorld().spawnParticle(
                        Particle.DUST,
                        loc,
                        2,
                        0, 0, 0,
                        0,
                        new Particle.DustOptions(org.bukkit.Color.BLUE, 1.0f)
                );
                particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 2, Particle.SMOKE);

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.5, 0.5, 0.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 5);
                        target.damage(damage, player);

                        // 冻结效果
                        if (target instanceof LivingEntity) {
                            // 给目标添加极强的缓慢效果
                            ((LivingEntity) target).addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, 40, 255)); // 2秒冰冻

                            // 记录目标当前位置，禁止移动
                            final Location frozenLocation = target.getLocation().clone();
                            final java.util.concurrent.atomic.AtomicInteger ticksLeft = new java.util.concurrent.atomic.AtomicInteger((int) freezeDurationTicks);

                            new BukkitRunnable() {
                                @Override
                                public void run() {
                                    if (ticksLeft.get() <= 0 || !target.isValid() || target.isDead()) {
                                        cancel();
                                        return;
                                    }

                                    // 强制目标回到冻结位置
                                    target.teleport(frozenLocation);

                                    // 继续显示冰冻粒子效果
                                    particleHelper.displayParticle(
                                            target.getLocation().add(0, 1, 0),
                                            0.5f, 0.5f, 0.5f,
                                            0,
                                            3,
                                            Particle.ITEM_SNOWBALL
                                    );

                                    ticksLeft.decrementAndGet();
                                }
                            }.runTaskTimer(Shoot.this, 0, 1);
                        }

                        // 命中提示
                        if (settings.isHitTitle() && entity instanceof Player) {
                            Player targetPlayer = (Player) entity;
                            sendTitle(targetPlayer, 5, 10, 5, "§b你被冻结了!", "§e移动被限制2秒");
                        }

                        // 特殊伤害
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);
                                target.damage(specialDamage, player);
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty() && entity instanceof Player) {
                            playSoundSafely((Player) entity, entity.getLocation(), hitSound, "BLOCK_GLASS_BREAK", 1.0f, 1.0f);
                        }

                        // 显示命中特效
                        particleHelper.displaySphere(target.getLocation().add(0, 1, 0), 1.0f, 30, Particle.ITEM_SNOWBALL);

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    /**
     * 处理雷击枪的射击效果，发射绿色暴击粒子，并在命中后对周围生物造成闪电和特殊伤害
     *
     * @param player 射击的玩家
     * @param gunId 武器ID
     * @param settings 显示设置
     */
    private void handleLightningGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone(); // 子弹起始位置
            int distance = 0; // 子弹飞行距离
            double maxDistance = 100; // 雷击枪的更长射程

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                // 移动子弹
                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 绿色暴击粒子效果
                loc.getWorld().spawnParticle(
                        Particle.DUST,
                        loc,
                        2,
                        0, 0, 0,
                        0,
                        new Particle.DustOptions(org.bukkit.Color.GREEN, 1.0f)
                );

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.5, 0.5, 0.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 5);
                        target.damage(damage, player);

                        // 创建闪电效果
                        target.getWorld().strikeLightningEffect(target.getLocation());

                        // 对范围内所有生物造成雷电和特殊伤害
                        double specialDamage = 0;
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                specialDamage = Double.parseDouble(specialDamageStr);
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 对10*10范围内的其他生物造成闪电和特殊伤害
                        for (Entity nearby : target.getNearbyEntities(10, 10, 10)) {
                            if (nearby instanceof LivingEntity && nearby != player) {
                                // 创建闪电效果
                                nearby.getWorld().strikeLightningEffect(nearby.getLocation());

                                // 造成特殊伤害
                                if (specialDamage > 0) {
                                    ((LivingEntity) nearby).damage(specialDamage, player);
                                }

                                // 在受影响实体和主要目标之间创建电击线效果
                                particleHelper.displayLine(
                                        target.getLocation().add(0, 1, 0),
                                        nearby.getLocation().add(0, 1, 0),
                                        Particle.ENCHANTED_HIT
                                );
                            }
                        }

                        // 对主要目标也造成特殊伤害
                        if (specialDamage > 0) {
                            target.damage(specialDamage, player);
                        }

                        // 命中提示
                        if (settings.isHitTitle() && entity instanceof Player) {
                            Player targetPlayer = (Player) entity;
                            sendTitle(targetPlayer, 5, 10, 5, "§e你被雷击了!", "§c周围的生物也受到了影响");
                        }

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty() && entity instanceof Player) {
                            playSoundSafely((Player) entity, entity.getLocation(), hitSound, "ENTITY_LIGHTNING_BOLT_IMPACT", 1.0f, 1.0f);
                        }

                        // 显示大型命中特效
                        particleHelper.displayExplosion(target.getLocation().add(0, 1, 0), Particle.ENCHANTED_HIT, 50);

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    /**
     * 启动换弹冷却，期间玩家无法射击
     *
     * @param player 需要进行换弹的玩家
     * @param gunId 武器ID
     * @param reloadTimeSeconds 换弹冷却时间（秒）
     */
    private void startReloading(Player player, String gunId, int reloadTimeSeconds) {
        // 检查玩家是否已经在换弹
        if (isReloading.contains(player.getUniqueId())) {
            return;
        }

        // 确保玩家在isReloadingMap中有条目
        if (!isReloadingMap.containsKey(player)) {
            isReloadingMap.put(player, new HashMap<>());
        }

        // 设置玩家为正在换弹状态
        isReloadingMap.get(player).put(gunId, true);
        isReloading.add(player.getUniqueId());

        // 显示换弹中提示（使用ActionBar）
        player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                TextComponent.fromLegacyText(ChatColor.YELLOW + "换弹中"));

        // 获取配置文件中的换弹时间
        int configReloadTime = config.getInt("guns." + gunId + ".reload_time", 3);
        int reloadTime = configReloadTime * 20; // 将秒转换为tick，20tick = 1秒

        // 获取当前手持物品
        ItemStack gunItem = player.getInventory().getItemInMainHand();
        if (!isGunItem(gunItem)) {
            isReloading.remove(player.getUniqueId());
            // 确保玩家在isReloadingMap中有条目
            if (isReloadingMap.containsKey(player)) {
                isReloadingMap.get(player).put(gunId, false);
            }
            return;
        }

        // 保存原始枪械物品的引用，用于动画验证
        final ItemStack originalGunItem = gunItem.clone();
        final String originalGunDisplayName = gunItem.getItemMeta() != null ? gunItem.getItemMeta().getDisplayName() : "";

        // 创建进度条任务
        new BukkitRunnable() {
            int progress = 0;

            @Override
            public void run() {
                if (!player.isOnline() || progress >= reloadTime) {
                    if (player.isOnline()) {
                        // 完成换弹，更新弹药数量
                        String gunIdKey = gunId + "_total";
                        Map<String, Integer> playerAmmo = ammoMap.get(player);
                        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

                        // 初始化弹夹数据结构
                        if (!clipAmmoMap.containsKey(player)) {
                            clipAmmoMap.put(player, new HashMap<>());
                        }

                        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

                        // 获取弹夹容量
                        int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);

                        // 获取当前弹夹中的子弹数
                        int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

                        // 计算需要补充的子弹数量
                        int ammoNeeded = clipSize - currentClipAmmo;

                        // 如果总弹药数小于需要补充的数量，则只补充可用的弹药
                        int ammoToUse = Math.min(ammoNeeded, totalAmmo);

                        // 更新弹夹中的子弹数
                        playerClipAmmo.put(gunId, currentClipAmmo + ammoToUse);

                        // 减少总弹药数
                        totalAmmo -= ammoToUse;
                        playerAmmo.put(gunIdKey, totalAmmo);

                        // 修复换弹完成时物品数量更新bug - 在物品栏中查找原始枪械物品并更新
                        ItemStack targetGun = null;
                        int targetSlot = -1;

                        // 遍历玩家物品栏查找匹配的枪械物品
                        for (int i = 0; i < player.getInventory().getSize(); i++) {
                            ItemStack item = player.getInventory().getItem(i);
                            if (item != null && isGunItem(item)) {
                                String itemDisplayName = item.getItemMeta() != null ? item.getItemMeta().getDisplayName() : "";
                                // 检查是否是相同的枪械（通过显示名称和类型比较）
                                if (item.getType() == originalGunItem.getType() &&
                                    itemDisplayName.equals(originalGunDisplayName)) {
                                    targetGun = item;
                                    targetSlot = i;
                                    break;
                                }
                            }
                        }

                        // 如果找到了原始枪械物品，更新其数量和属性
                        if (targetGun != null) {
                            // 设置物品数量为弹夹中的子弹数
                            int newClipAmmo = playerClipAmmo.get(gunId);
                            targetGun.setAmount(newClipAmmo);

                            // 更新物品描述中的当前弹药数量和总弹药数量显示
                            ItemMeta meta = targetGun.getItemMeta();
                            if (meta != null && meta.hasLore()) {
                                List<String> lore = meta.getLore();
                                if (lore != null) {
                                    for (int i = 0; i < lore.size(); i++) {
                                        String line = lore.get(i);
                                        if (line.contains("当前弹药")) {
                                            lore.set(i, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + newClipAmmo);
                                        } else if (line.contains("总弹药")) {
                                            lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + totalAmmo);
                                        }
                                    }
                                    meta.setLore(lore);
                                }
                            }

                            // 恢复物品耐久度为满
                            if (meta instanceof Damageable) {
                                ((Damageable) meta).setDamage(0); // 设置为满耐久
                            }

                            targetGun.setItemMeta(meta);
                            // 更新物品栏中的物品
                            player.getInventory().setItem(targetSlot, targetGun);

                            // 播放换弹完成音效
                            playSoundSafely(player, player.getLocation(), "BLOCK_STONE_BUTTON_CLICK_ON", "BLOCK_STONE_BUTTON_CLICK_ON", 1.0f, 1.0f);

                            // 显示换弹完成提示
                            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                                    TextComponent.fromLegacyText(ChatColor.GREEN + "换弹完成！"));

                            // 强制更新玩家物品栏，确保物品数量更新
                            player.updateInventory();

                            // 使用updatePlayerXP方法更新经验值显示
                            updatePlayerXP(player, gunId);
                        }

                        // 移除换弹状态
                        if (isReloadingMap.containsKey(player)) {
                            isReloadingMap.get(player).put(gunId, false);
                        }

                    }
                    isReloading.remove(player.getUniqueId());
                    cancel();
                    return;
                }

                // 计算进度
                double percentage = (double) progress / reloadTime;

                // 显示简化的换弹提示
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.YELLOW + "换弹中"));

                // 播放换弹音效
                if (progress % 10 == 0) { // 每10个tick播放一次音效
                    playSoundSafely(player, player.getLocation(), "BLOCK_LEVER_CLICK", "BLOCK_LEVER_CLICK", 0.5f, 1.0f);
                }

                // 更新物品耐久度动画 - 修复切换物品时动画错误显示的bug
                // 在玩家物品栏中查找原始枪械物品并直接更新其耐久值
                ItemStack targetGun = null;
                int targetSlot = -1;

                // 遍历玩家物品栏查找匹配的枪械物品
                for (int i = 0; i < player.getInventory().getSize(); i++) {
                    ItemStack item = player.getInventory().getItem(i);
                    if (item != null && isGunItem(item)) {
                        String itemDisplayName = item.getItemMeta() != null ? item.getItemMeta().getDisplayName() : "";
                        // 检查是否是相同的枪械（通过显示名称和类型比较）
                        if (item.getType() == originalGunItem.getType() &&
                            itemDisplayName.equals(originalGunDisplayName)) {
                            targetGun = item;
                            targetSlot = i;
                            break;
                        }
                    }
                }

                // 如果找到了原始枪械物品，更新其耐久值动画
                if (targetGun != null) {
                    ItemMeta meta = targetGun.getItemMeta();
                    if (meta instanceof Damageable) {
                        Damageable damageable = (Damageable) meta;
                        // 计算耐久度（从最大耐久-1到0耐久）
                        int maxDurability = targetGun.getType().getMaxDurability();
                        // 从最大耐久-1（几乎无耐久）开始，随着进度增加而减少损坏值（增加耐久度）
                        int damageValue = (int) ((1 - percentage) * (maxDurability - 1));
                        damageable.setDamage(damageValue);
                        targetGun.setItemMeta(meta);
                        // 更新物品栏中的物品
                        player.getInventory().setItem(targetSlot, targetGun);
                    }
                }

                progress++;
            }
        }.runTaskTimer(this, 0L, 1L);
    }

    /**
     * 启动射击冷却，期间玩家无法射击
     *
     * @param player 需要进行冷却的玩家
     * @param gunId 武器ID
     * @param cooldownSeconds 射击冷却时间（秒）
     */
    private void startCooldown(Player player, String gunId, int cooldownSeconds) {
        // 确保玩家在isCooldownMap中有条目
        if (!isCooldownMap.containsKey(player)) {
            isCooldownMap.put(player, new HashMap<>());
        }

        // 设置玩家为正在冷却状态
        isCooldownMap.get(player).put(gunId, true);

        // 启动定时任务，计时冷却时间
        new BukkitRunnable() {
            @Override
            public void run() {
                // 确保玩家仍在线且在Map中有条目
                if (player.isOnline() && isCooldownMap.containsKey(player)) {
                    // 移除冷却状态
                    isCooldownMap.get(player).put(gunId, false);
                }
            }
        }.runTaskLater(this, cooldownSeconds * 20L);
    }

    /**
     * 处理左键点击，检测双击以手动进入换弹冷却并重置射击计数
     *
     * @param player 进行左键点击的玩家
     */
    private void handleLeftClick(Player player) {
        long currentTime = System.currentTimeMillis();
        long lastClickTime = lastLeftClickMap.getOrDefault(player, 0L);
        long timeDifference = currentTime - lastClickTime;

        // 双击间隔阈值（毫秒）
        long doubleClickThreshold = 300;

        if (timeDifference <= doubleClickThreshold) {
            // 双击检测通过，进入换弹冷却
            String gunId = currentGunMap.get(player);
            if (gunId == null) {
                return;
            }

            // 获取当前手持物品
            ItemStack gunItem = player.getInventory().getItemInMainHand();
            if (!isGunItem(gunItem)) {
                return;
            }

            // 检查总弹药数量
            String gunIdKey = gunId + "_total";
            Map<String, Integer> playerAmmo = ammoMap.get(player);
            if (playerAmmo == null) {
                ammoMap.put(player, new HashMap<>());
                playerAmmo = ammoMap.get(player);
            }
            int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

            // 获取弹夹容量
            int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);

            // 获取当前弹夹中的子弹数
            if (!clipAmmoMap.containsKey(player)) {
                clipAmmoMap.put(player, new HashMap<>());
            }
            Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);
            int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

            // 计算需要补充的子弹数量
            int ammoNeeded = clipSize - currentClipAmmo;

            // 如果总弹药数为0，不允许换弹（修复bug：即使弹夹中有子弹，总弹药为0时也不允许手动换弹）
            if (totalAmmo <= 0) {
                player.sendMessage(ChatColor.RED + "弹药不足以完成手动换弹！请补充弹药！");
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.RED + "弹药不足以完成手动换弹！"));
                return;
            }

            // 如果总弹药数不足以完全补满弹夹，但大于0，显示警告但允许换弹
            if (totalAmmo < ammoNeeded) {
                player.sendMessage(ChatColor.YELLOW + "弹药不足以完全补满弹夹，将使用剩余的" + totalAmmo + "发子弹！");
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.YELLOW + "弹药不足以完全补满弹夹！"));
            }

            // 注意：弹夹数据结构、当前弹夹子弹数和弹夹容量已在前面获取
            // 如果当前弹药已满，不需要换弹
            if (currentClipAmmo >= clipSize) {
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.GREEN + "弹夹已满！"));
                return;
            }

            // 使用reload_time而不是limit_shoot_time
            int reloadTime = config.getInt("guns." + gunId + ".reload_time", 3);
            startReloading(player, gunId, reloadTime);
        }

        // 更新最后一次左键点击时间
        lastLeftClickMap.put(player, currentTime);
    }

    /**
     * 判断武器是否真正耗尽子弹（同时考虑弹夹中的子弹和总子弹）
     *
     * @param player 玩家
     * @param gunId 武器ID
     * @return 如果弹夹中和总子弹都为0，返回true；否则返回false
     */
    private boolean isWeaponTrulyEmpty(Player player, String gunId) {
        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());

            // 如果玩家没有弹药数据，说明这是新获得的武器，应该有满弹药
            // 获取最大弹药量
            int maxAmmo = config.getInt("guns." + gunId + ".ammo", 300);
            int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);

            // 设置初始总弹药量
            ammoMap.get(player).put(gunId + "_total", maxAmmo);

            // 初始化弹夹数据结构
            if (!clipAmmoMap.containsKey(player)) {
                clipAmmoMap.put(player, new HashMap<>());
            }

            // 设置初始弹夹子弹数
            clipAmmoMap.get(player).put(gunId, clipSize);

            // 新获得的武器肯定不是空的
            return false;
        }

        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());

            // 如果玩家没有弹夹数据，说明这是新获得的武器，应该有满弹夹
            int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);
            clipAmmoMap.get(player).put(gunId, clipSize);

            // 新获得的武器肯定不是空的
            return false;
        }

        Map<String, Integer> playerAmmo = ammoMap.get(player);
        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

        // 获取总弹药数
        String gunIdKey = gunId + "_total";
        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

        // 获取弹夹中的子弹数
        int clipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

        // 如果玩家没有该武器的弹夹数据，说明这是新获得的武器，应该有满弹夹
        if (!playerClipAmmo.containsKey(gunId)) {
            int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);
            playerClipAmmo.put(gunId, clipSize);
            return false;
        }

        // 修复bug：先判断弹夹中是否还有子弹，如果弹夹中还有子弹，即使总子弹为0，也不应该判定为子弹耗尽
        // 只有当弹夹中的子弹数为0且总子弹数为0时，才判定为武器真正耗尽子弹
        return clipAmmo <= 0 && totalAmmo <= 0;
    }

    /**
     * 更新玩家的经验值显示，用于显示当前武器的弹药数量 经验值只是用来显示总弹药数，而不是作为总弹药数的存储
     *
     * @param player 玩家
     * @param gunId 当前手持武器的ID
     */
    private void updatePlayerXP(Player player, String gunId) {
        if (gunId == null) {
            player.setExp(0);
            player.setLevel(0);
            return;
        }

        // 获取枪支的最大弹药量和弹夹容量
        int maxAmmo = config.getInt("guns." + gunId + ".ammo", 300);
        int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
            // 如果玩家没有弹药数据，说明这是新获得的武器，应该有满弹药
            ammoMap.get(player).put(gunId + "_total", maxAmmo);
        }

        Map<String, Integer> playerAmmo = ammoMap.get(player);

        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());
            // 如果玩家没有弹夹数据，说明这是新获得的武器，应该有满弹夹
            clipAmmoMap.get(player).put(gunId, clipSize);
        }

        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

        // 初始化枪支状态数据结构
        if (!gunEmptyStatusMap.containsKey(player)) {
            gunEmptyStatusMap.put(player, new HashMap<>());
            // 如果玩家没有枪支状态数据，说明这是新获得的武器，应该设置为非弹药耗尽状态
            gunEmptyStatusMap.get(player).put(gunId, false);
        }

        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);

        // 检查枪支是否处于弹药耗尽状态
        if (playerGunStatus.getOrDefault(gunId, false)) {
            // 如果枪支已经处于弹药耗尽状态，直接设置经验值为0
            player.setExp(0);
            player.setLevel(0);
            return;
        }

        // 获取总弹药数 - 使用正确的键名
        String gunIdKey = gunId + "_total";

        // 如果没有该武器的总弹药数据，说明这是新获得的武器，应该有满弹药
        if (!playerAmmo.containsKey(gunIdKey)) {
            playerAmmo.put(gunIdKey, maxAmmo);
        }

        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, maxAmmo);

        // 检查玩家手中的物品是否为弹药耗尽的武器（钻石）
        ItemStack item = player.getInventory().getItemInMainHand();
        if (item != null && item.getType() == Material.DIAMOND && item.getItemMeta() != null
                && item.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {
            // 如果是弹药耗尽的武器，设置经验值为0
            player.setExp(0);
            player.setLevel(0);
            return;
        }

        // 计算经验值的进度（0.0 到 1.0）
        float exp = (float) totalAmmo / maxAmmo; // 使用总弹药数计算经验条
        if (exp > 1.0f) {
            exp = 1.0f;
        }
        if (exp < 0.0f) {
            exp = 0.0f;
        }

        // 设置经验值显示
        player.setExp(exp);
        player.setLevel(totalAmmo); // 使用等级显示总弹药数量
    }

    /**
     * 内部类，用于存储玩家的显示设置
     */
    private class DisplaySettings {

        private boolean startTitle; // 是否显示射击开始时的标题
        private boolean hitTitle;   // 是否显示命中目标时的标题

        public DisplaySettings(boolean startTitle, boolean hitTitle) {
            this.startTitle = startTitle;
            this.hitTitle = hitTitle;
        }

        public boolean isStartTitle() {
            return startTitle;
        }

        public void setStartTitle(boolean startTitle) {
            this.startTitle = startTitle;
        }

        public boolean isHitTitle() {
            return hitTitle;
        }

        public void setHitTitle(boolean hitTitle) {
            this.hitTitle = hitTitle;
        }
    }

    /**
     * 发送标题给玩家（替代TitleAPI）
     *
     * @param player 玩家
     * @param fadeIn 淡入时间（tick）
     * @param stay 停留时间（tick）
     * @param fadeOut 淡出时间（tick）
     * @param title 主标题
     * @param subtitle 副标题
     */
    private void sendTitle(Player player, int fadeIn, int stay, int fadeOut, String title, String subtitle) {
        if (player != null && player.isOnline()) {
            player.sendTitle(title, subtitle, fadeIn, stay, fadeOut);
        }
    }

    /**
     * 手动实现绕Y轴旋转向量的方法
     *
     * @param vector 向量
     * @param angle 旋转角度（弧度）
     * @return 旋转后的向量
     */
    private org.bukkit.util.Vector rotateVectorAroundY(org.bukkit.util.Vector vector, double angle) {
        double cos = Math.cos(angle);
        double sin = Math.sin(angle);
        double x = vector.getX() * cos - vector.getZ() * sin;
        double z = vector.getX() * sin + vector.getZ() * cos;
        return new org.bukkit.util.Vector(x, vector.getY(), z).normalize();
    }

    /**
     * 添加新的辅助方法用于向量旋转
     *
     * @param vector 向量
     * @param axis 旋转轴
     * @param angle 旋转角度（弧度）
     * @return 旋转后的向量
     */
    private org.bukkit.util.Vector rotateVector(org.bukkit.util.Vector vector, org.bukkit.util.Vector axis, double angle) {
        double x = vector.getX();
        double y = vector.getY();
        double z = vector.getZ();

        double u = axis.getX();
        double v = axis.getY();
        double w = axis.getZ();

        double cosAngle = Math.cos(angle);
        double sinAngle = Math.sin(angle);

        double xPrime = u * (u * x + v * y + w * z) * (1d - cosAngle)
                + x * cosAngle
                + (-w * y + v * z) * sinAngle;
        double yPrime = v * (u * x + v * y + w * z) * (1d - cosAngle)
                + y * cosAngle
                + (w * x - u * z) * sinAngle;
        double zPrime = w * (u * x + v * y + w * z) * (1d - cosAngle)
                + z * cosAngle
                + (-v * x + u * y) * sinAngle;

        return new org.bukkit.util.Vector(xPrime, yPrime, zPrime);
    }

    /**
     * 监听ProjectileHitEvent事件，用于处理火箭弹的特殊伤害
     *
     * @param event 投射物命中事件
     */
    @EventHandler
    public void onProjectileHit(ProjectileHitEvent event) {
        if (!(event.getEntity() instanceof Fireball)) {
            return; // 仅处理Fireball实体
        }

        Fireball fireball = (Fireball) event.getEntity();

        // 检查该Fireball是否由插件发射
        if (!fireballMap.containsKey(fireball)) {
            return;
        }

        Player shooter = fireballMap.get(fireball);
        String gunId = currentGunMap.get(shooter);
        if (gunId == null || !config.contains("guns." + gunId)) {
            return;
        }

        // 获取特殊伤害值
        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "");
        if (specialDamageStr.isEmpty()) {
            return; // 无特殊伤害
        }

        double specialDamage;
        try {
            specialDamage = Double.parseDouble(specialDamageStr);
        } catch (NumberFormatException e) {
            getLogger().warning("无效的特殊伤害值: " + specialDamageStr + " 在武器ID: " + gunId);
            return;
        }

        Location impactLoc = fireball.getLocation();

        // 检测爆炸范围内的生物并施加特殊伤害
        for (Entity entity : impactLoc.getWorld().getNearbyEntities(impactLoc, 3.0, 3.0, 3.0)) { // 爆炸范围半径3
            if (entity instanceof LivingEntity && entity != shooter) {
                ((LivingEntity) entity).damage(specialDamage, shooter);
            }
        }

        // 播放爆炸粒子效果
        // 旧代码
        // ParticleEffect.EXPLOSION_HUGE.send(Bukkit.getOnlinePlayers(), impactLoc, 0, 0, 0, 0, 1);
        // 新代码
        particleHelper.displayHugeExplosionParticle(Bukkit.getOnlinePlayers(), impactLoc, 0, 0, 0, 0, 1);

        // 播放爆炸音效
        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
        if (!hitSound.isEmpty()) {
            playSoundSafely(shooter, impactLoc, hitSound, "ENTITY_GENERIC_EXPLODE", 1.0f, 1.0f);
        }

        // 确保玩家有显示设置，如果没有则从DeathZombieV4同步
        initializePlayerDisplaySettings(shooter);

        // 获取玩家的显示设置
        DisplaySettings settings = displaySettingsMap.get(shooter);

        // 显示击中提示
        if (settings.isHitTitle()) {
            sendTitle(shooter, 5, 20, 5, ChatColor.RED + "击中！", ChatColor.YELLOW + "你的火箭筒击中了目标！");
        }

        // 移除Fireball与玩家的关联
        fireballMap.remove(fireball);
    }

    /**
     * 给玩家添加指定ID的枪支
     *
     * @param player 目标玩家
     * @param itemId 武器ID
     */
    public void giveGunToPlayer(Player player, String itemId) {
        // 将Buy.yml中的武器ID映射到config.yml中的武器ID
        String configGunId = mapBuyIdToConfigId(itemId);

        if (config.contains("guns." + configGunId)) {
            String gunName = config.getString("guns." + configGunId + ".name", "未知武器");

            // 检查玩家是否已经持有相同的武器
            boolean hasGun = false;
            for (ItemStack item : player.getInventory().getContents()) {
                if (item != null && isGunItem(item)) {
                    ItemMeta meta = item.getItemMeta();
                    if (meta != null && meta.hasDisplayName()) {
                        String displayName = ChatColor.stripColor(meta.getDisplayName());
                        if (displayName.equals(gunName)) {
                            hasGun = true;
                            break;
                        }
                    }
                }
            }

            // 如果玩家已经持有相同的武器，则补充弹药而不是重新给予武器
            if (hasGun) {
                // 更新当前手持武器ID
                currentGunMap.put(player, configGunId);

                // 补充弹药
                replenishPlayerAmmo(player);

                player.sendMessage(ChatColor.GREEN + "你已经拥有 " + ChatColor.GOLD + gunName
                        + ChatColor.GREEN + "，已为你补充弹药！");
                return;
            }

            String materialName = config.getString("guns." + configGunId + ".material", "WOODEN_HOE");
            Material material = Material.getMaterial(materialName);
            if (material == null) {
                material = Material.WOODEN_HOE;
                getLogger().warning("无法找到材质 " + materialName + "，使用默认材质 WOODEN_HOE");
            }

            // 获取枪支弹夹容量
            int clipSize = config.getInt("guns." + configGunId + ".clip_size", 30);

            // 获取最大弹药量（总子弹数）
            int maxAmmo = config.getInt("guns." + configGunId + ".ammo", 300);

            // 创建武器物品，设置数量为弹夹容量
            ItemStack weapon = new ItemStack(material);
            // 设置物品数量为弹夹容量（修复bug：确保枪支获取后是满子弹状态）
            weapon.setAmount(clipSize);

            ItemMeta meta = weapon.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + gunName);

                // 添加武器描述
                List<String> lore = new ArrayList<>();
                lore.add(ChatColor.GRAY + "武器类型: " + ChatColor.GREEN + "远程武器");
                lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                lore.add(ChatColor.GRAY + "弹夹容量: " + ChatColor.YELLOW + clipSize);
                lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);

                // 根据武器ID添加特定描述
                addGunLore(lore, configGunId);

                lore.add(ChatColor.YELLOW + "右键点击射击，左键双击换弹");
                meta.setLore(lore);
                weapon.setItemMeta(meta);
            }

            // 将武器添加到玩家物品栏
            player.getInventory().addItem(weapon);
            player.sendMessage(ChatColor.GREEN + "已获得武器: " + ChatColor.GOLD + gunName
                    + ChatColor.GREEN + " (弹夹容量: " + clipSize + ", 总弹药: " + maxAmmo + ")");

            // 更新当前手持武器ID
            currentGunMap.put(player, configGunId);

            // 初始化弹药数据结构 - 确保总是创建新的Map实例
            if (!ammoMap.containsKey(player)) {
                ammoMap.put(player, new HashMap<>());
            }
            Map<String, Integer> playerAmmo = ammoMap.get(player);

            // 初始化弹夹数据结构 - 确保总是创建新的Map实例
            if (!clipAmmoMap.containsKey(player)) {
                clipAmmoMap.put(player, new HashMap<>());
            }
            Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

            // 初始化枪支状态数据结构 - 确保总是创建新的Map实例
            if (!gunEmptyStatusMap.containsKey(player)) {
                gunEmptyStatusMap.put(player, new HashMap<>());
            }
            Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);

            // 设置枪支状态为非弹药耗尽 - 这是关键修复点
            playerGunStatus.put(configGunId, false);

            // 设置初始总弹药量 - 确保使用正确的变量
            playerAmmo.put(configGunId + "_total", maxAmmo);

            // 设置初始弹夹子弹数 - 确保使用正确的变量
            playerClipAmmo.put(configGunId, clipSize);

            // 打印调试信息，确认弹药已正确设置
            getLogger().info("为玩家 " + player.getName() + " 设置武器 " + configGunId + " 的弹药: 弹夹=" + clipSize + ", 总弹药=" + maxAmmo);

            // 确保物品数量显示正确的弹夹子弹数
            if (weapon.getAmount() != clipSize) {
                weapon.setAmount(clipSize);
            }

            // 设置经验值显示（只用于显示总弹药数）
            player.setLevel(maxAmmo);

            // 设置经验条显示
            float exp = (float) maxAmmo / maxAmmo;
            if (exp > 1.0f) {
                exp = 1.0f;
            }
            player.setExp(exp);

            // 播放获得武器音效
            playSoundSafely(player, player.getLocation(), "ENTITY_ITEM_PICKUP", "ENTITY_ITEM_PICKUP", 1.0f, 1.0f);
        } else {
            player.sendMessage(ChatColor.RED + "无效的武器ID！");
            getLogger().warning("尝试给予玩家 " + player.getName() + " 无效的武器ID: " + itemId);
        }
    }

    private void addGunLore(List<String> lore, String gunId) {
        switch (gunId) {
            case "id1": // 手枪
                lore.add(ChatColor.GRAY + "武器说明: " + ChatColor.WHITE + "标准配发的手枪，");
                lore.add(ChatColor.WHITE + "可靠的性能和适中的伤害使其成为不错的选择。");
                lore.add(ChatColor.GRAY + "基础伤害: " + ChatColor.RED + "15");
                lore.add(ChatColor.GRAY + "射击冷却: " + ChatColor.YELLOW + "1秒");
                lore.add(ChatColor.GRAY + "弹药容量: " + ChatColor.AQUA + "5/300");
                break;
            // ... 其他武器的说明 ...
            case "id24": // 循声炮
                lore.add(ChatColor.GRAY + "武器说明: " + ChatColor.WHITE + "强大的音波武器，");
                lore.add(ChatColor.WHITE + "发射高能音波，造成巨大伤害。");
                lore.add(ChatColor.GRAY + "基础伤害: " + ChatColor.RED + "300");
                lore.add(ChatColor.GRAY + "射击冷却: " + ChatColor.YELLOW + "1.5秒");
                lore.add(ChatColor.GRAY + "弹药容量: " + ChatColor.AQUA + "5/50");
                break;
            case "id25": // 火焰喷射器
                lore.add(ChatColor.GRAY + "武器说明: " + ChatColor.WHITE + "强大的火焰喷射武器，");
                lore.add(ChatColor.WHITE + "可以喷射高温火焰，点燃敌人并造成持续伤害。");
                lore.add(ChatColor.GRAY + "基础伤害: " + ChatColor.RED + "5/tick");
                lore.add(ChatColor.GRAY + "射击冷却: " + ChatColor.YELLOW + "0.5秒");
                lore.add(ChatColor.GRAY + "弹药容量: " + ChatColor.AQUA + "10/100");
                break;
        }
    }

    /**
     * 将Buy.yml中的武器ID映射到config.yml中的武器ID
     *
     * @param buyId Buy.yml中的武器ID
     * @return config.yml中对应的武器ID
     */
    private String mapBuyIdToConfigId(String buyId) {
        // 注意：Buy.yml和config.yml中的ID对应关系需要手动维护
        switch (buyId) {
            case "id1":
                return "id1"; // 手枪 (现在Buy.yml中id1已经是手枪了)
            case "id2":
                return "id2"; // 步枪
            case "id3":
                return "id3"; // 霰弹枪
            case "id4":
                return "id4"; // 机枪
            case "id5":
                return "id5"; // 火箭筒
            case "id6":
                return "id6"; // 电击枪
            case "id7":
                return "id7"; // 狙击步枪
            case "id8":
                return "id8"; // 冷冻枪
            case "id9":
                return "id9"; // 雷击枪
            case "id10":
                return "id10"; // 压强枪
            case "id11":
                return "id11"; // 突击步枪 (现在Buy.yml中id11已经是突击步枪了)
            case "id12":
                return "id12"; // 冲锋枪
            case "id13":
                return "id13"; // 等离子枪
            case "id14":
                return "id14"; // 死神收割者
            case "id15":
                return "id15"; // 毁灭者
            case "id25":
                return "id25"; // 火焰喷射器
            default:
                return buyId;
        }
    }

    /**
     * 补充玩家当前手持武器的子弹
     *
     * @param player 目标玩家
     * @return 是否成功补充
     */
    public boolean replenishPlayerAmmo(Player player) {
        String currentGunId = currentGunMap.get(player);
        if (currentGunId == null) {
            // 检查玩家手中是否持有弹药耗尽的武器（钻石）
            ItemStack item = player.getInventory().getItemInMainHand();
            if (item != null && item.getType() == Material.DIAMOND && item.getItemMeta() != null
                    && item.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {

                // 从lore中获取原始枪支ID
                ItemMeta meta = item.getItemMeta();
                if (meta.hasLore()) {
                    List<String> lore = meta.getLore();
                    if (lore != null) {
                        for (String line : lore) {
                            if (line.contains("gun_id:")) {
                                // 提取gun_id后面的内容
                                currentGunId = ChatColor.stripColor(line).replace("gun_id:", "").trim();
                                break;
                            }
                        }
                    }
                }

                if (currentGunId == null) {
                    player.sendMessage(ChatColor.RED + "无法识别弹药耗尽的武器类型！");
                    return false;
                }
            } else {
                // 尝试在物品栏中寻找任何枪支
                boolean foundGun = false;
                for (ItemStack invItem : player.getInventory().getContents()) {
                    if (invItem != null && isGunItem(invItem)) {
                        ItemMeta meta = invItem.getItemMeta();
                        if (meta != null && meta.hasDisplayName()) {
                            String displayName = ChatColor.stripColor(meta.getDisplayName());
                            // 尝试从配置中找到匹配的武器ID
                            ConfigurationSection gunsSection = config.getConfigurationSection("guns");
                            if (gunsSection != null) {
                                for (String id : gunsSection.getKeys(false)) {
                                    String gunName = config.getString("guns." + id + ".name", "未知武器");
                                    if (displayName.equals(gunName)) {
                                        currentGunId = id;
                                        foundGun = true;
                                        break;
                                    }
                                }
                            }
                            if (foundGun) {
                                break;
                            }
                        }
                    }
                }

                if (!foundGun) {
                    player.sendMessage(ChatColor.RED + "你没有任何可以补充弹药的武器！");
                    return false;
                }
            }
        }

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }

        // 初始化枪支状态数据结构
        if (!gunEmptyStatusMap.containsKey(player)) {
            gunEmptyStatusMap.put(player, new HashMap<>());
        }

        // 重置枪支弹药耗尽状态
        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);
        playerGunStatus.put(currentGunId, false);

        int maxAmmo = config.getInt("guns." + currentGunId + ".ammo", 10);
        int clipSize = config.getInt("guns." + currentGunId + ".clip_size", 30);

        // 设置总弹药量
        ammoMap.get(player).put(currentGunId + "_total", maxAmmo);

        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());
        }

        // 设置弹夹子弹数为满
        clipAmmoMap.get(player).put(currentGunId, clipSize);

        // 确保物品数量显示正确的弹夹子弹数（修复bug：确保枪支获取后是满子弹状态）
        ItemStack mainHandItem = player.getInventory().getItemInMainHand();
        if (mainHandItem != null && mainHandItem.getItemMeta() != null) {
            // 检查是否是枪支物品
            if (isGunItem(mainHandItem)) {
                mainHandItem.setAmount(clipSize);
            } // 检查是否是弹药耗尽的武器（钻石）
            else if (mainHandItem.getType() == Material.DIAMOND
                    && mainHandItem.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {
                // 这种情况在后面会被处理，将钻石物品替换为正常枪支
            }
        }
        // 检查玩家手中是否持有弹药耗尽的武器（钻石）
        if (mainHandItem != null && mainHandItem.getType() == Material.DIAMOND && mainHandItem.getItemMeta() != null
                && mainHandItem.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {

            // 获取原始武器名称
            String gunName = "";
            ItemMeta meta = mainHandItem.getItemMeta();
            if (meta.hasLore()) {
                List<String> lore = meta.getLore();
                if (lore != null) {
                    for (String line : lore) {
                        if (line.contains("原武器:")) {
                            gunName = ChatColor.stripColor(line).replace("原武器:", "").trim();
                            break;
                        }
                    }
                }
            }

            // 创建新的武器物品
            String materialName = config.getString("guns." + currentGunId + ".material", "WOODEN_HOE");
            Material material = Material.getMaterial(materialName);
            if (material == null) {
                material = Material.WOODEN_HOE;
            }

            ItemStack newGunItem = new ItemStack(material);
            newGunItem.setAmount(clipSize);

            ItemMeta newMeta = newGunItem.getItemMeta();
            if (newMeta != null) {
                newMeta.setDisplayName(ChatColor.GOLD + gunName);

                // 添加武器描述
                List<String> newLore = new ArrayList<>();
                newLore.add(ChatColor.GRAY + "武器类型: " + ChatColor.GREEN + "远程武器");
                newLore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                newLore.add(ChatColor.GRAY + "弹夹容量: " + ChatColor.YELLOW + clipSize);
                newLore.add(ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);

                // 根据武器ID添加特定描述
                addGunLore(newLore, currentGunId);

                newLore.add(ChatColor.YELLOW + "右键点击射击，左键双击换弹");
                newMeta.setLore(newLore);
                newGunItem.setItemMeta(newMeta);
            }

            // 替换玩家手中的武器
            player.getInventory().setItemInMainHand(newGunItem);
        } else if (isGunItem(mainHandItem)) {
            // 如果是普通枪支，只更新弹药数量
            mainHandItem.setAmount(clipSize);

            // 更新物品描述中的当前弹药数量和总弹药数量显示
            ItemMeta meta = mainHandItem.getItemMeta();
            if (meta != null && meta.hasLore()) {
                List<String> lore = meta.getLore();
                if (lore != null) {
                    for (int i = 0; i < lore.size(); i++) {
                        String line = lore.get(i);
                        if (line.contains("当前弹药")) {
                            lore.set(i, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                        } else if (line.contains("总弹药")) {
                            lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);
                        }
                    }
                    meta.setLore(lore);
                    mainHandItem.setItemMeta(meta);
                }
            }

            player.getInventory().setItemInMainHand(mainHandItem);
        } else {
            // 如果玩家手中没有持有武器，尝试在物品栏中找到对应的武器并更新
            boolean updated = false;
            for (int i = 0; i < player.getInventory().getSize(); i++) {
                ItemStack invItem = player.getInventory().getItem(i);
                if (invItem != null && isGunItem(invItem)) {
                    ItemMeta meta = invItem.getItemMeta();
                    if (meta != null && meta.hasDisplayName()) {
                        String displayName = ChatColor.stripColor(meta.getDisplayName());
                        String gunName = config.getString("guns." + currentGunId + ".name", "未知武器");
                        if (displayName.equals(gunName)) {
                            // 更新弹药数量
                            invItem.setAmount(clipSize);

                            // 更新物品描述
                            if (meta.hasLore()) {
                                List<String> lore = meta.getLore();
                                if (lore != null) {
                                    for (int j = 0; j < lore.size(); j++) {
                                        String line = lore.get(j);
                                        if (line.contains("当前弹药")) {
                                            lore.set(j, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                                        } else if (line.contains("总弹药")) {
                                            lore.set(j, ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);
                                        }
                                    }
                                    meta.setLore(lore);
                                    invItem.setItemMeta(meta);
                                }
                            }

                            player.getInventory().setItem(i, invItem);
                            updated = true;
                            break;
                        }
                    }
                }
            }

            if (!updated) {
                // 如果没有找到对应的武器，可能是弹药耗尽的武器
                for (int i = 0; i < player.getInventory().getSize(); i++) {
                    ItemStack invItem = player.getInventory().getItem(i);
                    if (invItem != null && invItem.getType() == Material.DIAMOND && invItem.getItemMeta() != null
                            && invItem.getItemMeta().getDisplayName().contains("弹药耗尽的武器")) {

                        // 从lore中获取原始枪支ID
                        ItemMeta meta = invItem.getItemMeta();
                        String gunId = null;
                        String gunName = "";

                        if (meta.hasLore()) {
                            List<String> lore = meta.getLore();
                            if (lore != null) {
                                for (String line : lore) {
                                    if (line.contains("gun_id:")) {
                                        gunId = ChatColor.stripColor(line).replace("gun_id:", "").trim();
                                    } else if (line.contains("原武器:")) {
                                        gunName = ChatColor.stripColor(line).replace("原武器:", "").trim();
                                    }
                                }
                            }
                        }

                        if (gunId != null && gunId.equals(currentGunId)) {
                            // 创建新的武器物品
                            String materialName = config.getString("guns." + currentGunId + ".material", "WOODEN_HOE");
                            Material material = Material.getMaterial(materialName);
                            if (material == null) {
                                material = Material.WOODEN_HOE;
                            }

                            ItemStack newGunItem = new ItemStack(material);
                            newGunItem.setAmount(clipSize);

                            ItemMeta newMeta = newGunItem.getItemMeta();
                            if (newMeta != null) {
                                newMeta.setDisplayName(ChatColor.GOLD + gunName);

                                // 添加武器描述
                                List<String> newLore = new ArrayList<>();
                                newLore.add(ChatColor.GRAY + "武器类型: " + ChatColor.GREEN + "远程武器");
                                newLore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + clipSize);
                                newLore.add(ChatColor.GRAY + "弹夹容量: " + ChatColor.YELLOW + clipSize);
                                newLore.add(ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + maxAmmo);

                                // 根据武器ID添加特定描述
                                addGunLore(newLore, currentGunId);

                                newLore.add(ChatColor.YELLOW + "右键点击射击，左键双击换弹");
                                newMeta.setLore(newLore);
                                newGunItem.setItemMeta(newMeta);
                            }

                            player.getInventory().setItem(i, newGunItem);
                            updated = true;
                            break;
                        }
                    }
                }
            }

            if (!updated) {
                player.sendMessage(ChatColor.YELLOW + "已补充弹药，但未找到对应的武器在物品栏中。");
            }
        }

        player.sendMessage(ChatColor.GREEN + "你的武器已补充满子弹！");
        updatePlayerXP(player, currentGunId);

        // 播放补充弹药音效
        playSoundSafely(player, player.getLocation(), "ENTITY_EXPERIENCE_ORB_PICKUP", "ENTITY_EXPERIENCE_ORB_PICKUP", 1.0f, 1.0f);

        return true;
    }

    /**
     * 控制玩家的标题显示设置
     *
     * @param player 目标玩家
     * @param type 显示类型 ("start" 或 "hit")
     * @param enable 是否启用
     * @return 是否成功设置
     */
    public boolean setPlayerDisplay(Player player, String type, boolean enable) {
        // 确保玩家有显示设置，如果没有则从DeathZombieV4同步
        initializePlayerDisplaySettings(player);
        DisplaySettings settings = displaySettingsMap.get(player);

        switch (type) {
            case "start":
                settings.setStartTitle(enable);
                player.sendMessage(enable ? ChatColor.GREEN + "已开启射击开始时的标题显示！" : ChatColor.GREEN + "已关闭射击开始时的标题显示！");
                break;
            case "hit":
                settings.setHitTitle(enable);
                player.sendMessage(enable ? ChatColor.GREEN + "已开启命中目标时的标题显示！" : ChatColor.GREEN + "已关闭命中目标时的标题显示！");
                break;
            default:
                player.sendMessage(ChatColor.RED + "无效的显示类型！使用 start 或 hit。");
                return false;
        }

        displaySettingsMap.put(player, settings);
        return true;
    }

    /**
     * 从DeathZombieV4插件获取玩家的显示设置
     *
     * @param player 目标玩家
     * @return 玩家的显示设置，如果无法获取则返回默认设置
     */
    private DisplaySettings getPlayerDisplaySettingsFromDeathZombie(Player player) {
        if (deathZombiePlugin == null) {
            return new DisplaySettings(true, true);
        }

        try {
            // 获取PlayerDisplaySettingsManager实例
            Object deathZombieInstance = deathZombiePlugin;
            Method getPlayerDisplaySettingsManagerMethod = deathZombieInstance.getClass()
                .getMethod("getPlayerDisplaySettingsManager");
            Object settingsManager = getPlayerDisplaySettingsManagerMethod.invoke(deathZombieInstance);

            if (settingsManager != null) {
                // 获取玩家设置
                Method getPlayerSettingsMethod = settingsManager.getClass()
                    .getMethod("getPlayerSettings", Player.class);
                Object playerSettings = getPlayerSettingsMethod.invoke(settingsManager, player);

                if (playerSettings != null) {
                    // 获取射击和命中显示设置
                    Method isShootTitleMethod = playerSettings.getClass().getMethod("isShootTitle");
                    Method isHitTitleMethod = playerSettings.getClass().getMethod("isHitTitle");

                    boolean shootTitle = (Boolean) isShootTitleMethod.invoke(playerSettings);
                    boolean hitTitle = (Boolean) isHitTitleMethod.invoke(playerSettings);

                    getLogger().info("从DeathZombieV4获取玩家 " + player.getName() + " 的显示设置: 射击=" + shootTitle + ", 命中=" + hitTitle);
                    return new DisplaySettings(shootTitle, hitTitle);
                }
            }
        } catch (Exception e) {
            getLogger().warning("从DeathZombieV4获取玩家显示设置时出错: " + e.getMessage());
        }

        // 如果获取失败，返回默认设置
        return new DisplaySettings(true, true);
    }

    /**
     * 初始化玩家的显示设置（从DeathZombieV4同步）
     *
     * @param player 目标玩家
     */
    private void initializePlayerDisplaySettings(Player player) {
        if (!displaySettingsMap.containsKey(player)) {
            DisplaySettings settings = getPlayerDisplaySettingsFromDeathZombie(player);
            displaySettingsMap.put(player, settings);
            getLogger().info("为玩家 " + player.getName() + " 初始化显示设置: 射击=" + settings.isStartTitle() + ", 命中=" + settings.isHitTitle());
        }
    }

    /**
     * 重新加载 Shoot 插件的配置文件
     */
    public void reloadShootConfig() {
        reloadConfig();
        config = getConfig();
        getLogger().info(ChatColor.GREEN + "Shoot 配置文件已重新加载！");
    }

    /**
     * 检查玩家当前武器是否在冷却中
     *
     * @param player 目标玩家
     * @return 是否在冷却中
     */
    public boolean isPlayerOnCooldown(Player player) {
        String gunId = currentGunMap.get(player);
        if (gunId == null) {
            return false;
        }
        return isCooldownMap.getOrDefault(player, new HashMap<>()).getOrDefault(gunId, false);
    }

    /**
     * 获取玩家当前武器的剩余子弹数量
     *
     * @param player 目标玩家
     * @return 剩余子弹数量，若无武器则返回 -1
     */
    public int getPlayerAmmo(Player player) {
        String gunId = currentGunMap.get(player);
        if (gunId == null) {
            return -1;
        }
        if (!ammoMap.containsKey(player) || !ammoMap.get(player).containsKey(gunId)) {
            return -1;
        }
        return ammoMap.get(player).get(gunId);
    }

    /**
     * 设置玩家当前武器的子弹数量
     *
     * @param player 目标玩家
     * @param ammo 要设置的子弹数量
     * @return 是否成功设置
     */
    public boolean setPlayerAmmo(Player player, int ammo) {
        String gunId = currentGunMap.get(player);
        if (gunId == null) {
            return false;
        }
        if (!ammoMap.containsKey(player) || !ammoMap.get(player).containsKey(gunId)) {
            return false;
        }
        ammoMap.get(player).put(gunId, ammo);
        updatePlayerXP(player, gunId);
        return true;
    }

    /**
     * 监听玩家聊天事件，实现快速传送功能
     *
     * @param event 玩家聊天事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player sender = event.getPlayer();
        String message = event.getMessage();

        // 检查玩家是否拥有快速传送机会
        if (teleportCountMap.getOrDefault(sender, 0) > 0) {
            // 尝试根据消息中的玩家名找到目标玩家
            Player target = Bukkit.getPlayerExact(message);
            if (target != null && target.isOnline()) {
                // 传送发送者到目标玩家的位置
                sender.teleport(target.getLocation());
                sender.sendMessage(ChatColor.GREEN + "已传送到 " + ChatColor.AQUA + target.getName() + ChatColor.GREEN + " 的位置！");
                // 减少传送次数
                teleportCountMap.put(sender, teleportCountMap.get(sender) - 1);
                // 取消聊天消息的发送
                event.setCancelled(true);
            }
        }
    }

    /**
     * 打开枪支和弹药的 GUI 界面
     *
     * @param player 玩家
     */
    private void openShootGUI(Player player) {
        Inventory gui = Bukkit.createInventory(player, 54, ChatColor.BLUE + "枪支和弹药");

        // 添加枪支
        for (String gunId : config.getConfigurationSection("guns").getKeys(false)) {
            String gunName = config.getString("guns." + gunId + ".name", "未知武器");
            String materialName = config.getString("guns." + gunId + ".material", "WOOD_SPADE");
            Material material = Material.getMaterial(materialName);
            if (material == null) {
                material = Material.WOODEN_SHOVEL; // 默认材质
            }

            ItemStack gunItem = new ItemStack(material);
            ItemMeta meta = gunItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + gunName);
                meta.setLore(Arrays.asList(ChatColor.GREEN + "点击获取该武器"));
                gunItem.setItemMeta(meta);
            }

            gui.addItem(gunItem);
        }

        // 添加弹药
        for (String ammoId : config.getConfigurationSection("guns").getKeys(false)) {
            String ammoName = config.getString("guns." + ammoId + ".name", "未知弹药");
            ItemStack ammoItem = new ItemStack(Material.ARROW);
            ItemMeta meta = ammoItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + ammoName + " 弹药");
                meta.setLore(Arrays.asList(ChatColor.GREEN + "点击补充当前手持武器的弹药"));
                ammoItem.setItemMeta(meta);
            }

            gui.addItem(ammoItem);
        }

        player.openInventory(gui);
    }

    /**
     * 打开购买物品的 GUI 界面
     *
     * @param player 玩家
     */
    private void openBuyGUI(Player player) {
        Inventory gui = Bukkit.createInventory(player, 54, ChatColor.BLUE + "购买物品");

        // 添加护甲
        for (String armorId : buyConfig.getConfigurationSection("ar").getKeys(false)) {
            String armorName = buyConfig.getString("ar." + armorId + ".name", "未知护甲");
            ItemStack armorItem = new ItemStack(Material.LEATHER_CHESTPLATE);
            ItemMeta meta = armorItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + armorName);
                meta.setLore(Arrays.asList(ChatColor.GREEN + "点击购买该护甲"));
                armorItem.setItemMeta(meta);
            }

            gui.addItem(armorItem);
        }

        // 添加武器
        for (String weaponId : buyConfig.getConfigurationSection("wp").getKeys(false)) {
            String weaponName = buyConfig.getString("wp." + weaponId + ".name", "未知武器");
            ItemStack weaponItem = new ItemStack(Material.IRON_SWORD);
            ItemMeta meta = weaponItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + weaponName);
                meta.setLore(Arrays.asList(ChatColor.GREEN + "点击购买该武器"));
                weaponItem.setItemMeta(meta);
            }

            gui.addItem(weaponItem);
        }

        // 添加道具
        for (String itemId : buyConfig.getConfigurationSection("it").getKeys(false)) {
            String itemName = buyConfig.getString("it." + itemId + ".name", "未知道具");
            ItemStack item = new ItemStack(Material.POTION);
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + itemName);
                meta.setLore(Arrays.asList(ChatColor.GREEN + "点击购买该道具"));
                item.setItemMeta(meta);
            }

            gui.addItem(item);
        }

        // 添加特殊功能
        for (String specialId : buyConfig.getConfigurationSection("sp").getKeys(false)) {
            String specialName = buyConfig.getString("sp." + specialId + ".name", "未知功能");
            ItemStack specialItem = new ItemStack(Material.NETHER_STAR);
            ItemMeta meta = specialItem.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(ChatColor.GOLD + specialName);
                meta.setLore(Arrays.asList(ChatColor.GREEN + "点击购买该功能"));
                specialItem.setItemMeta(meta);
            }

            gui.addItem(specialItem);
        }

        player.openInventory(gui);
    }

    /**
     * 监听 InventoryClickEvent 事件，处理 GUI 点击和防止枪支移动
     * 使用LOWEST优先级确保最早拦截
     *
     * @param event 事件
     */
    @EventHandler(priority = EventPriority.LOWEST)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        Inventory gui = event.getInventory();
        InventoryView view = event.getView(); // 获取InventoryView对象
        ItemStack clickedItem = event.getCurrentItem();
        ItemStack cursorItem = event.getCursor();

        // 检查是否涉及枪支物品的操作（包括点击的物品和光标上的物品）
        boolean isGunInvolved = false;
        if (clickedItem != null && isGunItem(clickedItem)) {
            isGunInvolved = true;
        }
        if (cursorItem != null && isGunItem(cursorItem)) {
            isGunInvolved = true;
        }

        // 如果涉及枪支且不是在GUI界面中，则阻止操作
        if (isGunInvolved && !isPluginGUI(view)) {
            // 检查是否是丢弃操作（点击物品栏外部）
            if (event.getClickedInventory() == null ||
                event.getClick().toString().contains("DROP") ||
                event.getAction().toString().contains("DROP")) {
                event.setCancelled(true);

                // 只有在启用错误提示时才发送消息
                if (isErrorMessageEnabled()) {
                    player.sendMessage(ChatColor.RED + "不能丢弃枪支物品！");
                }
                return;
            }

            // 检查是否是移动枪支的操作
            if (event.getAction().toString().contains("MOVE") ||
                event.getAction().toString().contains("PLACE") ||
                event.getAction().toString().contains("SWAP")) {
                event.setCancelled(true);

                // 只有在启用错误提示时才发送消息
                if (isErrorMessageEnabled()) {
                    player.sendMessage(ChatColor.RED + "不能移动枪支物品！");
                }
                return;
            }
        }

        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            return;
        }

        // 优先处理GUI界面的点击事件，避免被枪支移动限制阻止

        // 处理枪支和弹药 GUI
        if (view.getTitle().equals(ChatColor.BLUE + "枪支和弹药")) {
            event.setCancelled(true);

            String itemName = ChatColor.stripColor(clickedItem.getItemMeta().getDisplayName());

            // 检查是否是枪支
            for (String gunId : config.getConfigurationSection("guns").getKeys(false)) {
                String gunName = config.getString("guns." + gunId + ".name", "未知武器");
                if (itemName.equals(gunName)) {
                    // 给予玩家枪支
                    giveGunToPlayer(player, gunId);
                    player.closeInventory();
                    return;
                }
            }

            // 检查是否是弹药
            for (String ammoId : config.getConfigurationSection("guns").getKeys(false)) {
                String ammoName = config.getString("guns." + ammoId + ".name", "未知弹药");
                if (itemName.equals(ammoName + " 弹药")) {
                    // 补充玩家当前手持武器的弹药
                    replenishPlayerAmmo(player);
                    player.closeInventory();
                    return;
                }
            }
        }

        // 处理购买物品 GUI
        if (view.getTitle().equals(ChatColor.BLUE + "购买物品")) {
            event.setCancelled(true);

            String itemName = ChatColor.stripColor(clickedItem.getItemMeta().getDisplayName());

            // 检查是否是护甲
            for (String armorId : buyConfig.getConfigurationSection("ar").getKeys(false)) {
                String armorName = buyConfig.getString("ar." + armorId + ".name", "未知护甲");
                if (itemName.equals(armorName)) {
                    // 给予玩家护甲
                    giveArmorToPlayer(player, armorId);
                    player.closeInventory();
                    return;
                }
            }

            // 检查是否是武器
            for (String weaponId : buyConfig.getConfigurationSection("wp").getKeys(false)) {
                String weaponName = buyConfig.getString("wp." + weaponId + ".name", "未知武器");
                if (itemName.equals(weaponName)) {
                    // 给予玩家武器
                    giveGunToPlayer(player, weaponId);
                    player.closeInventory();
                    return;
                }
            }

            // 检查是否是道具
            for (String itemId : buyConfig.getConfigurationSection("it").getKeys(false)) {
                String itemNameConfig = buyConfig.getString("it." + itemId + ".name", "未知道具");
                if (itemName.equals(itemNameConfig)) {
                    // 给予玩家道具
                    giveItemToPlayer(player, itemId);
                    player.closeInventory();
                    return;
                }
            }

            // 检查是否是特殊功能
            for (String specialId : buyConfig.getConfigurationSection("sp").getKeys(false)) {
                String specialName = buyConfig.getString("sp." + specialId + ".name", "未知功能");
                if (itemName.equals(specialName)) {
                    // 激活特殊功能
                    activateSpecialFunction(specialId);
                    player.closeInventory();
                    return;
                }
            }
        }

        // 处理DeathZombieV4的KitGUI相关界面，允许在这些界面中点击枪支物品
        String guiTitle = view.getTitle();
        if (isDeathZombieKitGUI(guiTitle)) {
            // 在KitGUI中允许点击枪支物品，不执行枪支移动限制
            getLogger().info("检测到KitGUI界面，允许枪支操作: " + guiTitle);
            return;
        }

        // 如果不是GUI界面的点击，则检查枪支移动限制
        // 但是，如果是任何形式的KitGUI相关界面，都应该允许操作
        // 因为在KitGUI中我们处理的是物品配置，而不是真正的枪支移动

        // 额外检查：如果GUI标题包含任何KitGUI相关关键词，都允许操作
        if (guiTitle != null) {
            String cleanTitle = ChatColor.stripColor(guiTitle);
            if (cleanTitle.contains("编辑器") || cleanTitle.contains("选择") ||
                cleanTitle.contains("确认") || cleanTitle.contains("槽位") ||
                cleanTitle.contains("护甲") || cleanTitle.contains("物品") ||
                cleanTitle.contains("武器") || cleanTitle.contains("类别")) {
                getLogger().info("检测到可能的KitGUI界面，允许所有操作: " + guiTitle);
                return;
            }
        }

        // 检查是否是枪支物品，如果是则取消移动
        if (clickedItem != null && isGunItem(clickedItem)) {
            getLogger().info("阻止枪支移动，GUI标题: " + guiTitle + ", 物品: " + clickedItem.getType());
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "不能移动枪支物品！");
            }
            return;
        }

        // 检查光标物品是否是枪支（使用之前已定义的cursorItem变量）
        if (cursorItem != null && isGunItem(cursorItem)) {
            getLogger().info("阻止枪支移动（光标），GUI标题: " + guiTitle + ", 物品: " + cursorItem.getType());
            event.setCancelled(true);

            // 只有在启用错误提示时才发送消息
            if (isErrorMessageEnabled()) {
                player.sendMessage(ChatColor.RED + "不能移动枪支物品！");
            }
            return;
        }
    }

    /**
     * 安全播放音效
     *
     * @param player 玩家
     * @param location 位置
     * @param soundName 音效名称
     * @param defaultSound 默认音效
     * @param volume 音量
     * @param pitch 音调
     */
    private void playSoundSafely(Player player, Location location, String soundName, String defaultSound, float volume, float pitch) {
        try {
            // 如果音效名称为空，使用默认音效
            if (soundName == null || soundName.isEmpty()) {
                soundName = "minecraft:" + defaultSound.toLowerCase().replace("_", ".");
            }

            // 如果音效名称不包含命名空间，添加minecraft:前缀
            if (!soundName.contains(":")) {
                soundName = "minecraft:" + soundName.toLowerCase().replace("_", ".");
            }

            // 尝试播放音效
            player.playSound(location, soundName, volume, pitch);
        } catch (Exception e) {
            // 如果播放失败，尝试使用默认音效
            try {
                String fallbackSound = "minecraft:" + defaultSound.toLowerCase().replace("_", ".");
                player.playSound(location, fallbackSound, volume, pitch);
            } catch (Exception ex) {
                // 如果默认音效也失败，使用通用爆炸音效
                try {
                    player.playSound(location, "minecraft:entity.generic.explode", volume, pitch);
                } catch (Exception exc) {
                    // 如果所有尝试都失败，记录错误但不中断游戏
                    getLogger().warning("无法播放音效: " + soundName + " 或默认音效: " + defaultSound);
                }
            }
        }
    }

    /**
     * API：创建购买点NPC
     *
     * @param location NPC的生成位置
     * @param npcName NPC的名称
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 创建的NPC对象，如果创建失败则返回null
     */
    public NPC createBuyPointNPC(Location location, String npcName, String type, String itemId) {
        if (!buyConfig.contains(type + "." + itemId)) {
            return null;
        }

        // 获取物品名称和价格
        String itemName = buyConfig.getString(type + "." + itemId + ".name", "未知物品");
        int price = buyConfig.getInt(type + "." + itemId + ".price", 0);

        // 创建NPC，使用村民类型而非玩家类型，避免僵尸攻击商人
        NPC npc = CitizensAPI.getNPCRegistry().createNPC(EntityType.VILLAGER, npcName);
        npc.spawn(location);
        npc.setProtected(true); // 设置为受保护状态

        // 保存NPC位置
        String npcUUID = npc.getUniqueId().toString();
        locConfig.set("npcs." + npcUUID + ".type", type);
        locConfig.set("npcs." + npcUUID + ".itemId", itemId);
        locConfig.set("npcs." + npcUUID + ".name", npcName);
        locConfig.set("npcs." + npcUUID + ".location.world", location.getWorld().getName());
        locConfig.set("npcs." + npcUUID + ".location.x", location.getX());
        locConfig.set("npcs." + npcUUID + ".location.y", location.getY());
        locConfig.set("npcs." + npcUUID + ".location.z", location.getZ());
        locConfig.set("npcs." + npcUUID + ".location.yaw", location.getYaw());
        locConfig.set("npcs." + npcUUID + ".location.pitch", location.getPitch());

        saveAllConfigs();

        // 不再创建悬浮文字，价格信息将显示在NPC名称中
        getLogger().info("成功创建购买点NPC: " + npcName + "，价格: " + price + "金币");

        return npc;
    }

    /**
     * API：获取指定类型和ID的物品价格
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 物品价格，如果不存在则返回-1
     */
    public int getItemPrice(String type, String itemId) {
        if (!buyConfig.contains(type + "." + itemId)) {
            return -1;
        }
        return buyConfig.getInt(type + "." + itemId + ".price", 0);
    }

    /**
     * API：获取指定类型和ID的物品名称
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 物品名称，如果不存在则返回null
     */
    public String getItemName(String type, String itemId) {
        if (!buyConfig.contains(type + "." + itemId)) {
            return null;
        }
        return buyConfig.getString(type + "." + itemId + ".name", "未知物品");
    }

    /**
     * API：检查指定类型和ID的物品是否存在
     *
     * @param type 物品类型 (ar|wp|it|sp)
     * @param itemId 物品ID
     * @return 如果物品存在则返回true，否则返回false
     */
    public boolean itemExists(String type, String itemId) {
        return buyConfig.contains(type + "." + itemId);
    }

    /**
     * API：获取Shoot插件的实例
     *
     * @return Shoot插件实例
     */
    public static Shoot getInstance() {
        return instance;
    }

    /**
     * 检查是否启用错误提示消息
     *
     * @return 如果启用错误提示则返回true，否则返回false
     */
    private boolean isErrorMessageEnabled() {
        return config.getBoolean("error_messages.enabled", true);
    }

    /**
     * 检查是否是DeathZombieV4的KitGUI相关界面
     *
     * @param title GUI标题
     * @return 如果是KitGUI相关界面则返回true
     */
    private boolean isDeathZombieKitGUI(String title) {
        if (title == null) {
            return false;
        }

        // 移除颜色代码进行比较
        String cleanTitle = ChatColor.stripColor(title);

        // 检查各种KitGUI相关的标题
        return cleanTitle.contains("初始装备编辑器") ||
               cleanTitle.contains("物品选择") ||
               cleanTitle.contains("选择物品") ||  // 新增：统一GUI的标题
               cleanTitle.contains("物品类别选择") ||
               cleanTitle.contains("确认选择") ||
               cleanTitle.contains("槽位") && cleanTitle.contains("选项") ||
               cleanTitle.contains("确认更改类型") ||
               cleanTitle.contains("槽位类型选择") ||
               cleanTitle.contains("护甲套装") ||
               title.startsWith(ChatColor.GOLD + "物品选择") ||
               title.startsWith(ChatColor.AQUA + "选择物品") ||  // 新增：统一GUI的颜色标题
               title.startsWith(ChatColor.BLUE + "选择") && title.contains("护甲套装");
    }

    // 处理等离子枪的射击效果
    private void handlePlasmaGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 显示射击开始标题
        if (settings.isStartTitle()) {
            sendTitle(player, 5, 20, 5, ChatColor.BLUE + "等离子枪！",
                    ChatColor.AQUA + "等离子能量发射！");
        }

        // 播放射击音效
        playSoundSafely(player, player.getLocation(), "ENTITY_GUARDIAN_ATTACK", "ENTITY_GUARDIAN_ATTACK", 1.0f, 0.5f);

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 60;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                // 移动子弹
                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 灵魂火焰粒子效果
                particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 3, Particle.SOUL_FIRE_FLAME);
                particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 2, Particle.SOUL);

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.8, 0.8, 0.8)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 120);
                        target.damage(damage, player);

                        // 等离子灼烧效果
                        target.setFireTicks(100); // 5秒燃烧
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);
                                target.damage(specialDamage, player);
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 命中特效
                        particleHelper.displayParticle(target.getLocation(), 0.5f, 1f, 0.5f, 0.1f, 30, Particle.SOUL_FIRE_FLAME);
                        particleHelper.displayParticle(target.getLocation(), 0.5f, 1f, 0.5f, 0.1f, 20, Particle.SOUL);

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_GENERIC_EXPLODE", 1.0f, 1.0f);
                        }

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.BLUE + "等离子击中！",
                                    ChatColor.AQUA + "目标被等离子灼烧！");
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    // 处理死神收割者的射击效果
    private void handleReaperGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 显示射击开始标题
        if (settings.isStartTitle()) {
            sendTitle(player, 5, 20, 5, ChatColor.DARK_PURPLE + "死神收割者！",
                    ChatColor.LIGHT_PURPLE + "死神之力觉醒！");
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 70;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                // 移动子弹
                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 龙息粒子效果
                particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0.05f, 5, Particle.DRAGON_BREATH);
                particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 2, Particle.END_ROD);

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.0, 1.0, 1.0)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 200);
                        target.damage(damage, player);

                        // 死神收割效果
                        target.addPotionEffect(new PotionEffect(PotionEffectType.WITHER, 100, 2)); // 5秒凋零效果
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);
                                target.damage(specialDamage, player);
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 命中特效
                        particleHelper.displayParticle(target.getLocation(), 0.5f, 1f, 0.5f, 0.1f, 40, Particle.DRAGON_BREATH);
                        particleHelper.displayParticle(target.getLocation(), 0.5f, 1f, 0.5f, 0.1f, 20, Particle.END_ROD);

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_WITHER_DEATH", 1.0f, 1.0f);
                        }

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.DARK_PURPLE + "死神收割！",
                                    ChatColor.LIGHT_PURPLE + "目标灵魂被收割！");
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    // 处理毁灭者的射击效果
    private void handleDestroyerGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 80;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                // 移动子弹
                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 爆炸粒子效果
                particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 3, Particle.EXPLOSION);
                particleHelper.displayParticle(loc, 0.3f, 0.3f, 0.3f, 0, 5, Particle.LAVA);

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 300);
                        target.damage(damage, player);

                        // 毁灭性爆炸效果
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);
                                // 对范围内所有实体造成伤害
                                for (Entity nearby : target.getNearbyEntities(5, 5, 5)) {
                                    if (nearby instanceof LivingEntity && nearby != player) {
                                        ((LivingEntity) nearby).damage(specialDamage, player);
                                    }
                                }
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 命中特效
                        particleHelper.displayParticle(target.getLocation(), 1f, 1f, 1f, 0.1f, 50, Particle.EXPLOSION);
                        particleHelper.displayParticle(target.getLocation(), 1f, 1f, 1f, 0.1f, 30, Particle.LAVA);
                        target.getWorld().createExplosion(target.getLocation(), 0F, false, false, player);

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_GENERIC_EXPLODE", 1.0f, 0.5f);
                        }

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.RED + "毁灭打击！",
                                    ChatColor.GOLD + "造成毁灭性伤害！");
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    // 添加新的武器处理方法
    private void handleSuperLaserGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 播放射击音效
        playSoundSafely(player, player.getLocation(), "ENTITY_GUARDIAN_ATTACK", "ENTITY_GUARDIAN_ATTACK", 1.0f, 0.5f);

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 100;
            float hue = 0;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 创建螺旋状的激光效果
                for (int i = 0; i < 3; i++) {
                    double angle = (distance * 0.5) + (i * (2 * Math.PI / 3));
                    double radius = 0.5;
                    double x = Math.cos(angle) * radius;
                    double y = Math.sin(angle) * radius;
                    Location particleLoc = loc.clone().add(x, y, 0);

                    // 彩虹色激光效果
                    hue = (hue + 0.01f) % 1.0f;
                    java.awt.Color color = java.awt.Color.getHSBColor(hue, 1.0f, 1.0f);
                    org.bukkit.Color bukkitColor = org.bukkit.Color.fromRGB(color.getRed(), color.getGreen(), color.getBlue());

                    // 主光束
                    loc.getWorld().spawnParticle(
                            Particle.DUST,
                            particleLoc,
                            2,
                            0, 0, 0,
                            0,
                            new Particle.DustOptions(bukkitColor, 1.5f)
                    );

                    // 能量光环效果
                    particleHelper.displayParticle(particleLoc, 0.1f, 0.1f, 0.1f, 0, 1, Particle.END_ROD);
                }

                // 添加中心光束
                particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.FLASH);

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        double damage = config.getDouble("guns." + gunId + ".damage", 500);
                        target.damage(damage, player);

                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "800");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);
                                target.damage(specialDamage, player);

                                // 创建能量爆炸效果
                                Location targetLoc = target.getLocation();
                                for (int i = 0; i < 360; i += 15) {
                                    double angle = Math.toRadians(i);
                                    double x = Math.cos(angle) * 2;
                                    double z = Math.sin(angle) * 2;
                                    Location explosionLoc = targetLoc.clone().add(x, 1, z);

                                    // 能量爆炸粒子
                                    particleHelper.displayParticle(explosionLoc, 0, 0, 0, 0.5f, 5, Particle.FLASH);
                                    particleHelper.displayParticle(explosionLoc, 0.2f, 0.2f, 0.2f, 0, 3, Particle.END_ROD);
                                }
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_GENERIC_EXPLODE", 2.0f, 0.5f);
                        }

                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.RED + "超级激光打击！",
                                    ChatColor.GOLD + "目标被能量湮灭！");
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    private void handleBlackHoleGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 播放射击音效
        playSoundSafely(player, player.getLocation(), "ENTITY_WITHER_SPAWN", "ENTITY_WITHER_SPAWN", 1.0f, 0.5f);

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 90;
            double rotation = 0;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;
                rotation += 0.2;

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 在击中位置创建持续的黑洞效果
                    new BukkitRunnable() {
                        int ticks = 0;
                        double currentRadius = 0.5;

                        @Override
                        public void run() {
                            if (ticks >= 100) { // 持续5秒
                                cancel();
                                return;
                            }

                            currentRadius = Math.min(3.0, currentRadius + 0.1);

                            // 创建黑洞视觉效果
                            for (int i = 0; i < 16; i++) {
                                double angle = (i * Math.PI * 2 / 16) + (ticks * 0.2);
                                double x = Math.cos(angle) * currentRadius;
                                double z = Math.sin(angle) * currentRadius;
                                Location particleLoc = loc.clone().add(x, 0, z);

                                // 黑洞粒子效果
                                particleLoc.getWorld().spawnParticle(
                                        Particle.DRAGON_BREATH,
                                        particleLoc,
                                        1,
                                        0, 0, 0,
                                        0
                                );

                                // 添加额外的粒子效果
                                particleLoc.getWorld().spawnParticle(
                                        Particle.PORTAL,
                                        particleLoc,
                                        2,
                                        0.1, 0.1, 0.1,
                                        0
                                );
                            }

                            // 对周围实体造成伤害和吸引效果
                            for (Entity entity : loc.getWorld().getNearbyEntities(loc, currentRadius + 2, currentRadius + 2, currentRadius + 2)) {
                                if (entity instanceof LivingEntity && entity != player) {
                                    LivingEntity target = (LivingEntity) entity;
                                    // 造成持续伤害
                                    target.damage(10, player);
                                    // 将实体吸向黑洞中心
                                    Vector pull = loc.toVector().subtract(target.getLocation().toVector());
                                    if (pull.lengthSquared() > 0.1) {
                                        pull.normalize().multiply(0.8);
                                        target.setVelocity(pull);
                                    }
                                }
                            }

                            ticks++;
                        }
                    }.runTaskTimer(Shoot.this, 0L, 1L);

                    // 播放黑洞音效
                    playSoundSafely(player, loc, "ENTITY_ENDERMAN_TELEPORT", "ENTITY_ENDERMAN_TELEPORT", 2.0f, 0.5f);

                    cancel();
                    return;
                }

                // 飞行过程中的粒子效果
                for (int i = 0; i < 8; i++) {
                    double angle = (i * Math.PI * 2 / 8) + rotation;
                    double x = Math.cos(angle) * 0.5;
                    double z = Math.sin(angle) * 0.5;
                    Location particleLoc = loc.clone().add(x, 0, z);

                    particleLoc.getWorld().spawnParticle(
                            Particle.DRAGON_BREATH,
                            particleLoc,
                            1,
                            0, 0, 0,
                            0
                    );
                }

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        double damage = config.getDouble("guns." + gunId + ".damage", 400);
                        target.damage(damage, player);

                        // 创建黑洞效果
                        new BukkitRunnable() {
                            int ticks = 0;
                            double currentRadius = 0.5;

                            @Override
                            public void run() {
                                if (ticks >= 100) {
                                    cancel();
                                    return;
                                }

                                currentRadius = Math.min(3.0, currentRadius + 0.1);

                                // 创建黑洞视觉效果
                                for (int i = 0; i < 16; i++) {
                                    double angle = (i * Math.PI * 2 / 16) + (ticks * 0.2);
                                    double x = Math.cos(angle) * currentRadius;
                                    double z = Math.sin(angle) * currentRadius;
                                    Location particleLoc = target.getLocation().clone().add(x, 1, z);

                                    particleLoc.getWorld().spawnParticle(
                                            Particle.DRAGON_BREATH,
                                            particleLoc,
                                            1,
                                            0, 0, 0,
                                            0
                                    );

                                    particleLoc.getWorld().spawnParticle(
                                            Particle.PORTAL,
                                            particleLoc,
                                            2,
                                            0.1, 0.1, 0.1,
                                            0
                                    );
                                }

                                // 对周围实体造成伤害和吸引效果
                                for (Entity nearby : target.getNearbyEntities(currentRadius + 2, currentRadius + 2, currentRadius + 2)) {
                                    if (nearby instanceof LivingEntity && nearby != player && nearby != target) {
                                        LivingEntity nearbyTarget = (LivingEntity) nearby;
                                        nearbyTarget.damage(10, player);
                                        Vector pull = target.getLocation().toVector().subtract(nearby.getLocation().toVector());
                                        if (pull.lengthSquared() > 0.1) {
                                            pull.normalize().multiply(0.8);
                                            nearby.setVelocity(pull);
                                        }
                                    }
                                }

                                ticks++;
                            }
                        }.runTaskTimer(Shoot.this, 0L, 1L);

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5,
                                    ChatColor.DARK_PURPLE + "黑洞吞噬！",
                                    ChatColor.LIGHT_PURPLE + "目标被黑洞吞噬！"
                            );
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    private void handleSonicRifle(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 播放射击音效
        playSoundSafely(player, player.getLocation(), "BLOCK_NOTE_BLOCK_PLING", "BLOCK_NOTE_BLOCK_PLING", 1.0f, 1.0f);

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = config.getDouble("guns." + gunId + ".range", 80);
            float note = 0;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;
                note = (note + 0.1f) % 2.0f;

                // 创建音符粒子效果
                for (int i = 0; i < 3; i++) {
                    double angle = i * Math.PI * 2 / 3;
                    double radius = Math.sin(distance * 0.2) * 0.5;
                    double x = Math.cos(angle) * radius;
                    double y = Math.sin(angle) * radius;
                    Location particleLoc = loc.clone().add(x, y, 0);

                    // 发送音符粒子
                    particleLoc.getWorld().spawnParticle(Particle.NOTE, particleLoc, 1, 0, 0, 0, note);
                }

                // 每隔一定距离播放音符音效
                if (distance % 5 == 0) {
                    playSoundSafely(player, loc, "BLOCK_NOTE_BLOCK_" + getRandomNoteSound(), "BLOCK_NOTE_BLOCK_PLING", 0.5f, note + 0.5f);
                }

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 80);
                        target.damage(damage, player);

                        // 命中特效
                        for (int i = 0; i < 20; i++) {
                            loc.getWorld().spawnParticle(Particle.NOTE, target.getLocation().add(0, 1, 0),
                                    1, 0.5, 0.5, 0.5, i * 0.1f);
                        }

                        // 播放命中音效组合
                        String[] notes = {"PLING", "HARP", "CHIME", "XYLOPHONE"};
                        for (String note : notes) {
                            playSoundSafely(player, target.getLocation(),
                                    "BLOCK_NOTE_BLOCK_" + note, "BLOCK_NOTE_BLOCK_PLING", 1.0f, 1.0f);
                        }

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.LIGHT_PURPLE + "音符命中！",
                                    ChatColor.AQUA + "目标被音乐能量击中！");
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    // 添加一个辅助方法来获取随机音符音效
    private String getRandomNoteSound() {
        String[] notes = {"PLING", "HARP", "CHIME", "XYLOPHONE", "BELL", "GUITAR", "BASS"};
        return notes[(int) (Math.random() * notes.length)];
    }

    private void handleEnergyPulseGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 播放射击音效
        playSoundSafely(player, player.getLocation(), "ENTITY_ILLUSIONER_CAST_SPELL", "ENTITY_ILLUSIONER_CAST_SPELL", 1.0f, 0.5f);

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 45;
            float hue = 0;
            double pulseSize = 0;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;
                hue = (hue + 0.05f) % 1.0f;
                pulseSize = Math.sin(distance * 0.2) * 0.5;

                // 创建能量脉冲波纹效果
                for (int ring = 0; ring < 2; ring++) {
                    double ringRadius = (0.5 + pulseSize) * (ring + 1);
                    int particles = 12;

                    for (int i = 0; i < particles; i++) {
                        double angle = ((double) i / particles) * 2 * Math.PI;
                        double x = Math.cos(angle) * ringRadius;
                        double y = Math.sin(angle) * ringRadius;
                        Location particleLoc = loc.clone().add(x, y, 0);

                        // 变色能量效果
                        java.awt.Color color = java.awt.Color.getHSBColor(hue, 0.8f, 1.0f);
                        org.bukkit.Color bukkitColor = org.bukkit.Color.fromRGB(color.getRed(), color.getGreen(), color.getBlue());

                        particleLoc.getWorld().spawnParticle(
                                Particle.DUST,
                                particleLoc,
                                1,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(bukkitColor, 1.0f)
                        );
                    }
                }

                // 中心能量核心效果
                particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.END_ROD);

                // 随机能量火花
                if (Math.random() < 0.3) {
                    Location sparkLoc = loc.clone().add(
                            Math.random() * 0.4 - 0.2,
                            Math.random() * 0.4 - 0.2,
                            Math.random() * 0.4 - 0.2
                    );
                    particleHelper.displayParticle(sparkLoc, 0, 0, 0, 0, 1, Particle.FALLING_NECTAR);
                }

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1, 1, 1)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        double damage = config.getDouble("guns." + gunId + ".damage", 100);
                        target.damage(damage, player);

                        // 能量脉冲效果
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "150");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);
                                target.damage(specialDamage, player);

                                // 创建能量爆发效果
                                new BukkitRunnable() {
                                    int ticks = 0;
                                    double radius = 0.5;

                                    @Override
                                    public void run() {
                                        if (ticks >= 20) {
                                            cancel();
                                            return;
                                        }

                                        radius += 0.2;
                                        Location targetLoc = target.getLocation();
                                        float currentHue = (hue + (float) ticks / 20) % 1.0f;
                                        java.awt.Color color = java.awt.Color.getHSBColor(currentHue, 0.8f, 1.0f);
                                        org.bukkit.Color bukkitColor = org.bukkit.Color.fromRGB(
                                                color.getRed(), color.getGreen(), color.getBlue());

                                        for (int i = 0; i < 360; i += 20) {
                                            double angle = Math.toRadians(i);
                                            double x = Math.cos(angle) * radius;
                                            double z = Math.sin(angle) * radius;
                                            Location pulseLoc = targetLoc.clone().add(x, 1, z);

                                            pulseLoc.getWorld().spawnParticle(
                                                    Particle.DUST,
                                                    pulseLoc,
                                                    1,
                                                    0, 0, 0,
                                                    0,
                                                    new Particle.DustOptions(bukkitColor, 1.0f)
                                            );
                                        }

                                        ticks++;
                                    }
                                }.runTaskTimer(Shoot.this, 0L, 1L);
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.LIGHT_PURPLE + "能量脉冲！",
                                    ChatColor.DARK_PURPLE + "目标被能量震荡！");
                        }

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.0f);
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    private void handleRainbowGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 35;
            int colorIndex = 0;
            org.bukkit.Color[] colors = {
                org.bukkit.Color.RED,
                org.bukkit.Color.ORANGE,
                org.bukkit.Color.YELLOW,
                org.bukkit.Color.GREEN,
                org.bukkit.Color.BLUE,
                org.bukkit.Color.PURPLE
            };

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 彩虹粒子效果
                colorIndex = (colorIndex + 1) % colors.length;
                loc.getWorld().spawnParticle(
                        Particle.DUST,
                        loc,
                        5,
                        0.1, 0.1, 0.1,
                        0,
                        new Particle.DustOptions(colors[colorIndex], 1)
                );

                // 检查是否击中方块
                if (!loc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                    // 显示击中效果
                    particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                    particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                    playSoundSafely(player, loc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                    cancel();
                    return;
                }

                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1, 1, 1)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        double damage = config.getDouble("guns." + gunId + ".damage", 60);
                        target.damage(damage, player);

                        // 彩虹效果
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "90");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);
                                target.damage(specialDamage, player);
                                // 添加随机正面效果
                                PotionEffectType[] effects = {
                                    PotionEffectType.SPEED,
                                    PotionEffectType.STRENGTH,
                                    PotionEffectType.REGENERATION,
                                    PotionEffectType.ABSORPTION
                                };
                                target.addPotionEffect(new PotionEffect(
                                        effects[(int) (Math.random() * effects.length)],
                                        100, 1));
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 特效
                        for (org.bukkit.Color color : colors) {
                            loc.getWorld().spawnParticle(
                                    Particle.DUST,
                                    target.getLocation(),
                                    10,
                                    0.5, 0.5, 0.5,
                                    0,
                                    new Particle.DustOptions(color, 1)
                            );
                        }

                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.GOLD + "彩虹喷射！",
                                    ChatColor.YELLOW + "目标被彩虹能量击中！");
                        }

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.0f);
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    private void handleTeleportGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 50;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    // 达到最大距离时传送到最后位置
                    teleportPlayer(player, loc, settings);
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 末影珍珠粒子效果
                particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 2, Particle.PORTAL);

                // 检查是否击中实体
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1, 1, 1)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        double damage = config.getDouble("guns." + gunId + ".damage", 50);
                        target.damage(damage, player);

                        // 特效
                        particleHelper.displayParticle(target.getLocation(), 0.5f, 0.5f, 0.5f, 0.1f, 20, Particle.PORTAL);

                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.DARK_PURPLE + "传送打击！",
                                    ChatColor.LIGHT_PURPLE + "成功命中目标！");
                        }

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.0f);
                        }

                        cancel();
                        return;
                    }
                }

                // 检查是否击中方块
                if (loc.getBlock().getType().isSolid()) {
                    // 如果击中方块，传送到方块前的最后一个安全位置
                    Location safeLocation = loc.clone().subtract(loc.getDirection());
                    teleportPlayer(player, safeLocation, settings);
                    cancel();
                    return;
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    // 辅助方法：处理传送效果
    private void teleportPlayer(Player player, Location destination, DisplaySettings settings) {
        // 传送前的粒子效果
        particleHelper.displayParticle(player.getLocation(), 0.5f, 1f, 0.5f, 0.1f, 30, Particle.PORTAL);

        // 执行传送
        player.teleport(destination);

        // 传送后的粒子效果 (瞬时)
        particleHelper.displayParticle(destination, 0.5f, 1f, 0.5f, 0.1f, 30, Particle.PORTAL);

        // 添加持续的传送后粒子效果
        new BukkitRunnable() {
            int durationTicks = 60; // 持续3秒 (20 ticks/秒)
            int elapsedTicks = 0;

            @Override
            public void run() {
                if (elapsedTicks >= durationTicks || !player.isOnline() || !player.getWorld().equals(destination.getWorld())) {
                    // 如果玩家下线、切换世界或持续时间结束，则取消
                    cancel();
                    return;
                }
                // 在目标位置持续生成粒子
                particleHelper.displayParticle(destination, 0.5f, 1f, 0.5f, 0.1f, 15, Particle.PORTAL);
                elapsedTicks++;
            }
        }.runTaskTimer(this, 5L, 1L); // 延迟5 ticks开始，每1 tick执行一次

        // 播放传送音效
        playSoundSafely(player, destination, "ENTITY_ENDERMAN_TELEPORT", "ENTITY_ENDERMAN_TELEPORT", 1.0f, 1.0f);

        if (settings.isHitTitle()) {
            sendTitle(player, 5, 20, 5, ChatColor.DARK_PURPLE + "传送成功！",
                    ChatColor.LIGHT_PURPLE + "你已到达目标位置！");
        }
    }

    /**
     * 处理突击步枪的射击效果 特点: 高射速、中等伤害、大弹夹、适合中距离战斗
     */
    private void handleAssaultRifle(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 显示射击开始标题
        if (settings.isStartTitle()) {
            sendTitle(player, 5, 20, 5, ChatColor.GOLD + "突击步枪！",
                    ChatColor.YELLOW + "高速射击模式！");
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 60; // 中等射程

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 暴击粒子效果
                particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 2, Particle.CRIT);

                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.8, 0.8, 0.8)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 45);
                        target.damage(damage, player);

                        // 命中特效
                        particleHelper.displayParticle(target.getLocation(), 0.3f, 0.3f, 0.3f, 0.1f, 15, Particle.CRIT);

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.0f);
                        }

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.RED + "命中！",
                                    ChatColor.YELLOW + "突击步枪击中目标！");
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    /**
     * 处理冲锋枪的射击效果 特点: 超高射速、较低伤害、中等弹夹、适合近距离战斗
     */
    private void handleSubmachineGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 显示射击开始标题
        if (settings.isStartTitle()) {
            sendTitle(player, 5, 20, 5, ChatColor.RED + "冲锋枪！",
                    ChatColor.YELLOW + "超高射速模式！");
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 40; // 较短射程,适合近战

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 暴击粒子效果
                particleHelper.displayParticle(loc, 0.1f, 0.1f, 0.1f, 0, 2, Particle.CRIT);

                // 由于射速很快,添加一些额外的粒子效果增强视觉效果
                if (distance % 2 == 0) {
                    particleHelper.displayParticle(loc, 0.05f, 0.05f, 0.05f, 0, 1, Particle.SMOKE);
                }

                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.8, 0.8, 0.8)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 30);
                        target.damage(damage, player);

                        // 命中特效
                        particleHelper.displayParticle(target.getLocation(), 0.3f, 0.3f, 0.3f, 0.1f, 10, Particle.CRIT);

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.2f);
                        }

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5, ChatColor.RED + "命中！",
                                    ChatColor.YELLOW + "冲锋枪击中目标！");
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    /**
     * 处理星辉主宰者的射击效果 特点：发送闪光特效，命中时造成爆炸伤害
     */
    private void handleStarlightDominatorGun(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 播放射击音效
        playSoundSafely(player, player.getLocation(), "ENTITY_FIREWORK_ROCKET_BLAST", "ENTITY_FIREWORK_ROCKET_BLAST", 1.0f, 0.5f);

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 75; // 较长射程
            float brightness = 1.0f;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;

                // 星辉闪光效果
                brightness = (float) Math.sin(distance * 0.2) * 0.5f + 0.5f;

                // 创建星辉粒子效果
                for (int i = 0; i < 4; i++) {
                    double angle = i * Math.PI / 2;
                    double radius = Math.sin(distance * 0.1) * 0.5;
                    double x = Math.cos(angle) * radius;
                    double y = Math.sin(angle) * radius;
                    Location particleLoc = loc.clone().add(x, y, 0);

                    // 发光粒子
                    loc.getWorld().spawnParticle(
                            Particle.DUST,
                            particleLoc,
                            3,
                            0.1, 0.1, 0.1,
                            0,
                            new Particle.DustOptions(org.bukkit.Color.fromRGB(255, 255, 200), brightness)
                    );

                    // 添加末影粒子增强视觉效果
                    particleHelper.displayParticle(particleLoc, 0.1f, 0.1f, 0.1f, 0, 1, Particle.END_ROD);
                }

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.2, 1.2, 1.2)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 250);
                        target.damage(damage, player);

                        // 爆炸效果和额外伤害
                        String specialDamageStr = config.getString("guns." + gunId + ".special_damage", "350");
                        if (!specialDamageStr.isEmpty()) {
                            try {
                                double specialDamage = Double.parseDouble(specialDamageStr);

                                // 对周围实体造成爆炸伤害
                                for (Entity nearby : target.getNearbyEntities(4, 4, 4)) {
                                    if (nearby instanceof LivingEntity && nearby != player) {
                                        ((LivingEntity) nearby).damage(specialDamage * 0.5, player);
                                    }
                                }

                                // 对主要目标造成全额伤害
                                target.damage(specialDamage, player);
                            } catch (NumberFormatException ignored) {
                            }
                        }

                        // 爆炸特效
                        particleHelper.displayParticle(target.getLocation(), 1f, 1f, 1f, 0.1f, 50, Particle.EXPLOSION);
                        particleHelper.displayParticle(target.getLocation(), 0.8f, 0.8f, 0.8f, 0.1f, 40, Particle.END_ROD);

                        // 创建无伤害的爆炸效果
                        target.getWorld().createExplosion(target.getLocation(), 0F, false, false, player);

                        // 播放命中音效
                        playSoundSafely(player, target.getLocation(), "ENTITY_GENERIC_EXPLODE", "ENTITY_GENERIC_EXPLODE", 1.0f, 0.8f);

                        // 显示命中提示
                        if (settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5,
                                    ChatColor.YELLOW + "✧ 星辉主宰 ✧",
                                    ChatColor.GOLD + "目标被星辉能量湮灭！"
                            );
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    private void handleVoidStardustGun(Player player, String gunId, Location targetLocation) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        World world = player.getWorld();
        Vector direction = targetLocation.toVector().subtract(player.getEyeLocation().toVector()).normalize();
        Location currentLocation = player.getEyeLocation().clone();

        // 创建三重粒子效果
        for (double t = 0; t < 65; t += 0.5) {
            currentLocation.add(direction.clone().multiply(0.5));

            // END_ROD 粒子 - 星光效果
            world.spawnParticle(Particle.END_ROD, currentLocation, 1, 0, 0, 0, 0);

            // PORTAL 粒子 - 虚空效果
            world.spawnParticle(Particle.PORTAL, currentLocation, 3, 0.1, 0.1, 0.1, 0.02);

            // DRAGON_BREATH 粒子 - 龙息效果
            world.spawnParticle(Particle.DRAGON_BREATH, currentLocation, 2, 0.1, 0.1, 0.1, 0.01);

            // 检查是否击中实体
            for (Entity entity : world.getNearbyEntities(currentLocation, 1, 1, 1)) {
                if (entity instanceof LivingEntity && entity != player) {
                    LivingEntity target = (LivingEntity) entity;

                    // 基础伤害
                    target.damage(200, player);

                    // 持续虚空伤害效果
                    new BukkitRunnable() {
                        int ticks = 0;
                        final double damagePerTick = 450.0 / 60.0; // 3秒内造成450点伤害(60ticks)

                        @Override
                        public void run() {
                            if (ticks >= 60 || target.isDead()) {
                                this.cancel();
                                return;
                            }

                            target.damage(damagePerTick, player);

                            // 额外虚空粒子效果
                            world.spawnParticle(Particle.PORTAL, target.getLocation().add(0, 1, 0),
                                    5, 0.3, 0.5, 0.3, 0.1);

                            ticks++;
                        }
                    }.runTaskTimer(this, 0L, 1L);

                    // 播放命中音效
                    world.playSound(target.getLocation(), Sound.ENTITY_ENDERMAN_SCREAM, 1.0f, 1.0f);
                    return;
                }
            }
        }
    }

    // 新增方法：处理火焰喷射器 (id25)
    private void handleFlamethrower(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 播放火焰喷射音效
        playSoundSafely(player, player.getLocation(), "BLOCK_FIRE_AMBIENT", "BLOCK_FIRE_AMBIENT", 1.0f, 0.8f);

        // 获取玩家视线方向
        Vector direction = player.getLocation().getDirection().normalize();
        Location startLoc = player.getEyeLocation().clone();

        // 创建火焰喷射效果
        new BukkitRunnable() {
            int ticks = 0;
            final int maxTicks = 20; // 持续1秒
            final double maxDistance = 10.0; // 最大射程
            final double spreadFactor = 0.3; // 火焰扩散因子

            @Override
            public void run() {
                if (ticks >= maxTicks) {
                    cancel();
                    return;
                }

                ticks++;

                // 创建多个火焰粒子流
                for (int i = 0; i < 5; i++) {
                    // 随机偏移原始方向，创建扩散效果
                    Vector offset = new Vector(
                            (Math.random() - 0.5) * spreadFactor,
                            (Math.random() - 0.5) * spreadFactor,
                            (Math.random() - 0.5) * spreadFactor
                    );

                    Vector flameDirection = direction.clone().add(offset).normalize();

                    // 创建火焰粒子轨迹
                    new BukkitRunnable() {
                        Location flameLoc = startLoc.clone();
                        int distance = 0;

                        @Override
                        public void run() {
                            if (distance > maxDistance || !flameLoc.getBlock().isPassable()) {
                                cancel();
                                return;
                            }

                            // 移动火焰位置
                            flameLoc.add(flameDirection.clone().multiply(0.5));
                            distance += 0.5;

                            // 显示火焰粒子
                            particleHelper.displayParticle(flameLoc, 0.1f, 0.1f, 0.1f, 0.01f, 2, Particle.FLAME);

                            // 随机添加烟雾粒子
                            if (Math.random() < 0.3) {
                                particleHelper.displayParticle(flameLoc, 0.1f, 0.1f, 0.1f, 0.01f, 1, Particle.SMOKE);
                            }

                            // 检测是否击中实体
                            for (Entity entity : flameLoc.getWorld().getNearbyEntities(flameLoc, 0.8, 0.8, 0.8)) {
                                if (entity instanceof LivingEntity && entity != player) {
                                    LivingEntity target = (LivingEntity) entity;

                                    // 造成伤害
                                    double damage = config.getDouble("guns." + gunId + ".damage", 5.0) / 5.0; // 每tick造成基础伤害的1/5
                                    target.damage(damage, player);

                                    // 设置目标着火
                                    int fireTicks = config.getInt("guns." + gunId + ".fire_ticks", 60); // 默认着火3秒
                                    if (target.getFireTicks() < fireTicks) {
                                        target.setFireTicks(fireTicks);
                                    }

                                    // 显示命中特效
                                    particleHelper.displayParticle(target.getLocation(), 0.3f, 0.5f, 0.3f, 0.05f, 5, Particle.FLAME);

                                    // 播放命中音效
                                    if (Math.random() < 0.2) { // 降低音效频率
                                        playSoundSafely(player, target.getLocation(), "ENTITY_GENERIC_BURN", "ENTITY_GENERIC_BURN", 0.5f, 1.2f);
                                    }

                                    // 显示命中提示
                                    if (settings.isHitTitle() && Math.random() < 0.1) { // 降低提示频率
                                        sendTitle(player, 5, 10, 5, ChatColor.RED + "命中！",
                                                ChatColor.GOLD + "火焰灼烧目标！");
                                    }
                                }
                            }
                        }
                    }.runTaskTimer(Shoot.this, 0, 1);
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    // 新增方法：处理循声炮 (id24)
    private void handleSonicCannon(Player player, String gunId, DisplaySettings settings) {
        // 处理通用的子弹逻辑
        if (!handleCommonBulletLogic(player, gunId)) {
            return; // 如果子弹逻辑处理失败，直接返回
        }

        // 播放初始射击音效
        playSoundSafely(player, player.getLocation(), "ENTITY_WARDEN_SONIC_BOOM", "ENTITY_WARDEN_SONIC_BOOM", 2.0f, 1.0f);

        // 显示射击开始标题
        if (settings.isStartTitle()) {
            sendTitle(player, 5, 20, 5, ChatColor.AQUA + "音波发射！",
                    ChatColor.DARK_AQUA + "循声炮已发射！");
        }

        new BukkitRunnable() {
            Location loc = player.getEyeLocation().clone();
            int distance = 0;
            double maxDistance = 80;
            double radius = 0.5;
            int tick = 0;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                loc.add(loc.getDirection().multiply(1));
                distance++;
                tick++;
                radius = Math.sin(distance * 0.1) * 0.5 + 1.0;

                // 主要音波环形粒子效果
                for (int ring = 0; ring < 3; ring++) {
                    double ringRadius = radius * (1 + ring * 0.5);
                    int particles = 12;
                    for (int i = 0; i < particles; i++) {
                        double angle = (i * Math.PI * 2) / particles + (tick * 0.2);
                        double x = Math.cos(angle) * ringRadius;
                        double y = Math.sin(angle) * ringRadius;
                        Location particleLoc = loc.clone().add(x, y, 0);

                        // 青色音波粒子
                        particleLoc.getWorld().spawnParticle(
                                Particle.DUST,
                                particleLoc,
                                1,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(org.bukkit.Color.fromRGB(0, 255, 255), 1.5f)
                        );
                    }
                }

                // 中心音波效果
                try {
                    loc.getWorld().spawnParticle(Particle.SONIC_BOOM, loc, 1, 0, 0, 0, 0);
                } catch (IllegalArgumentException e) {
                    // 如果SONIC_BOOM不可用，使用备用粒子效果
                    loc.getWorld().spawnParticle(Particle.CLOUD, loc, 3, 0.1, 0.1, 0.1, 0.05);
                }

                // 每隔5 ticks播放音效
                if (tick % 5 == 0) {
                    playSoundSafely(player, loc, "ENTITY_WARDEN_SONIC_CHARGE", "ENTITY_WARDEN_SONIC_CHARGE", 1.0f, 1.2f);
                }

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 1.5, 1.5, 1.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;

                        // 基础伤害
                        double damage = config.getDouble("guns." + gunId + ".damage", 300);
                        target.damage(damage, player);

                        // 命中特效
                        Location targetLoc = target.getLocation().add(0, 1, 0);

                        // 大型音波爆炸效果
                        for (int i = 0; i < 360; i += 10) {
                            double angle = Math.toRadians(i);
                            double explosionRadius = 3.0;
                            double x = Math.cos(angle) * explosionRadius;
                            double z = Math.sin(angle) * explosionRadius;
                            Location explosionLoc = targetLoc.clone().add(x, 0, z);

                            // 爆炸环粒子
                            explosionLoc.getWorld().spawnParticle(
                                    Particle.DUST,
                                    explosionLoc,
                                    3,
                                    0.1, 0.1, 0.1,
                                    0,
                                    new Particle.DustOptions(org.bukkit.Color.fromRGB(0, 255, 255), 2.0f)
                            );
                        }

                        // 添加额外的爆炸效果
                        try {
                            targetLoc.getWorld().spawnParticle(Particle.SONIC_BOOM, targetLoc, 3, 0.5, 0.5, 0.5, 0);
                        } catch (IllegalArgumentException e) {
                            // 使用粒子组合模拟爆炸效果
                            World world = targetLoc.getWorld();
                            // 中心爆炸效果
                            world.spawnParticle(Particle.CLOUD, targetLoc, 15, 0.5, 0.5, 0.5, 0.2);
                            world.spawnParticle(Particle.CRIT, targetLoc, 20, 0.5, 0.5, 0.5, 0.5);
                            world.spawnParticle(Particle.FLAME, targetLoc, 10, 0.3, 0.3, 0.3, 0.1);

                            // 创建圆形冲击波效果
                            for (double i = 0; i < Math.PI * 2; i += Math.PI / 8) {
                                double x = Math.cos(i) * 2;
                                double z = Math.sin(i) * 2;
                                Location waveLocation = targetLoc.clone().add(x, 0, z);
                                world.spawnParticle(Particle.CLOUD, waveLocation, 3, 0.1, 0.1, 0.1, 0);
                                world.spawnParticle(Particle.CRIT, waveLocation, 2, 0.1, 0.1, 0.1, 0.05);
                            }

                            // 添加向上扩散的粒子效果
                            for (double y = 0; y < 2; y += 0.2) {
                                Location upLoc = targetLoc.clone().add(0, y, 0);
                                world.spawnParticle(Particle.CLOUD, upLoc, 2, 0.2, 0, 0.2, 0);
                                world.spawnParticle(Particle.CRIT, upLoc, 3, 0.2, 0, 0.2, 0.1);
                            }
                        }

                        // 播放命中音效组合
                        playSoundSafely(player, targetLoc, "ENTITY_WARDEN_SONIC_BOOM", "ENTITY_WARDEN_SONIC_BOOM", 2.0f, 0.7f);
                        playSoundSafely(player, targetLoc, "ENTITY_GENERIC_EXPLODE", "ENTITY_GENERIC_EXPLODE", 1.0f, 0.5f);

                        // 显示命中提示
                        if (settings != null && settings.isHitTitle()) {
                            sendTitle(player, 5, 20, 5,
                                    ChatColor.AQUA + "音波冲击！",
                                    ChatColor.DARK_AQUA + "目标被音波震荡！"
                            );
                        }

                        // 对周围实体造成额外伤害
                        for (Entity nearby : target.getNearbyEntities(3, 3, 3)) {
                            if (nearby instanceof LivingEntity && nearby != player && nearby != target) {
                                ((LivingEntity) nearby).damage(damage * 0.5, player);
                            }
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    /**
     * 加载枪支材质列表
     */
    private void loadGunMaterials() {
        gunMaterials.clear();
        ConfigurationSection gunsSection = config.getConfigurationSection("guns");
        if (gunsSection != null) {
            for (String gunId : gunsSection.getKeys(false)) {
                String materialName = config.getString("guns." + gunId + ".material");
                if (materialName != null) {
                    Material material = Material.getMaterial(materialName);
                    if (material != null) {
                        gunMaterials.add(material);
                    }
                }
            }
        }
        getLogger().info("已加载 " + gunMaterials.size() + " 个枪支材质");
    }

    /**
     * 加载玩家数据
     */
    private void loadPlayerData() {
        // 清除现有数据
        playerMaxGuns.clear();

        // 从配置文件加载玩家数据
        if (playerDataConfig != null) {
            ConfigurationSection maxGunsSection = playerDataConfig.getConfigurationSection("max_guns");
            if (maxGunsSection != null) {
                for (String uuidStr : maxGunsSection.getKeys(false)) {
                    try {
                        UUID uuid = UUID.fromString(uuidStr);
                        int maxGuns = maxGunsSection.getInt(uuidStr, 2); // 默认值为2
                        playerMaxGuns.put(uuid, maxGuns);
                    } catch (IllegalArgumentException e) {
                        getLogger().warning("无效的UUID格式: " + uuidStr);
                    }
                }
            }
        }
        getLogger().info("已加载玩家枪支限制数据");
    }

    /**
     * 判断指定枪械是否应该忽略方块碰撞
     *
     * @param gunId 枪械ID
     * @return 是否忽略方块碰撞
     */
    private boolean shouldIgnoreBlocks(String gunId) {
        // 这些武器的子弹可以穿过方块
        return gunId.equals("id7")
                || // 狙击步枪
                gunId.equals("id10")
                || // 压强枪
                gunId.equals("id13")
                || // 等离子枪
                gunId.equals("id15")
                || // 毁灭者
                gunId.equals("id16")
                || // 超级激光炮
                gunId.equals("id19")
                || // 能量脉冲枪
                gunId.equals("id24");   // 循声炮
    }

    private void reloadGun(Player player, String gunId) {
        if (isReloading.contains(player.getUniqueId())) {
            return;
        }

        ItemStack gunItem = player.getInventory().getItemInMainHand();
        if (!isGunItem(gunItem)) {
            return;
        }

        // 获取枪支配置
        int reloadTimeSeconds = config.getInt("guns." + gunId + ".reload_time", 3); // 获取换弹时间（秒）
        int reloadTime = reloadTimeSeconds * 20; // 将秒转换为tick，20tick = 1秒
        int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);
        int currentAmmo = gunItem.getAmount();

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }

        // 初始化枪支状态数据结构
        if (!gunEmptyStatusMap.containsKey(player)) {
            gunEmptyStatusMap.put(player, new HashMap<>());
        }

        // 检查枪支是否处于弹药耗尽状态
        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);
        if (playerGunStatus.getOrDefault(gunId, false)) {
            // 如果枪支已经处于弹药耗尽状态，直接禁止换弹
            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            playSoundSafely(player, player.getLocation(), "ENTITY_VILLAGER_NO", "ENTITY_VILLAGER_NO", 1.0f, 1.0f);
            return;
        }

        Map<String, Integer> playerAmmo = ammoMap.get(player);
        String gunIdKey = gunId + "_total";

        // 获取总弹药数
        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

        // 如果总弹药数小于1，将枪支变成钻石并禁止换弹
        if (totalAmmo < 1) {
            // 设置枪支状态为弹药耗尽
            playerGunStatus.put(gunId, true);

            // 获取枪支名称
            String gunName = "";
            if (isGunItem(gunItem) && gunItem.getItemMeta() != null) {
                gunName = gunItem.getItemMeta().getDisplayName();
            }

            // 创建一个钻石物品
            ItemStack emptyGun = new ItemStack(Material.DIAMOND);
            ItemMeta emptyMeta = emptyGun.getItemMeta();
            if (emptyMeta != null) {
                // 设置显示名称
                emptyMeta.setDisplayName(ChatColor.RED + "弹药耗尽的武器");

                // 设置Lore
                List<String> lore = new ArrayList<>();
                lore.add(ChatColor.GRAY + "原武器: " + ChatColor.YELLOW + gunName);
                lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.RED + "0");
                lore.add("");
                lore.add(ChatColor.RED + "弹药已耗尽，无法射击");
                lore.add(ChatColor.YELLOW + "请使用 /shoot up 补充弹药");

                // 存储原始枪支ID，用于恢复
                lore.add(ChatColor.BLACK + "gun_id:" + gunId);

                emptyMeta.setLore(lore);
                emptyGun.setItemMeta(emptyMeta);
            }

            // 替换玩家手中的枪支
            player.getInventory().setItemInMainHand(emptyGun);

            // 设置经验值为0，显示弹药耗尽状态
            player.setLevel(0);
            player.setExp(0);

            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            playSoundSafely(player, player.getLocation(), "ENTITY_VILLAGER_NO", "ENTITY_VILLAGER_NO", 1.0f, 1.0f);
            return;
        }

        // 移除对弹夹子弹数的检查，直接禁止换弹
        // 检查已在上面完成
        // 开始换弹过程
        isReloading.add(player.getUniqueId());

        // 显示换弹中提示（使用ActionBar）
        player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                TextComponent.fromLegacyText(ChatColor.YELLOW + "换弹中"));

        // 创建进度条任务
        new BukkitRunnable() {
            int progress = 0;
            final ItemStack originalGun = gunItem.clone();
            final String originalGunDisplayName = gunItem.getItemMeta() != null ? gunItem.getItemMeta().getDisplayName() : "";

            @Override
            public void run() {
                if (!player.isOnline() || progress >= reloadTime) {
                    if (player.isOnline()) {
                        // 完成换弹，更新弹药数量
                        String gunIdKey = gunId + "_total";
                        Map<String, Integer> playerAmmo = ammoMap.get(player);
                        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

                        // 再次检查总弹药数，如果为0则禁止换弹
                        if (totalAmmo <= 0) {
                            // 设置枪进入子弹耗尽状态 - 修复：在物品栏中查找原始枪械物品
                            ItemStack targetGun = null;
                            int targetSlot = -1;

                            // 遍历玩家物品栏查找匹配的枪械物品
                            for (int i = 0; i < player.getInventory().getSize(); i++) {
                                ItemStack item = player.getInventory().getItem(i);
                                if (item != null && isGunItem(item)) {
                                    String itemDisplayName = item.getItemMeta() != null ? item.getItemMeta().getDisplayName() : "";
                                    // 检查是否是相同的枪械（通过显示名称和类型比较）
                                    if (item.getType() == originalGun.getType() &&
                                        itemDisplayName.equals(originalGunDisplayName)) {
                                        targetGun = item;
                                        targetSlot = i;
                                        break;
                                    }
                                }
                            }

                            // 如果找到了原始枪械物品，更新其状态
                            if (targetGun != null) {
                                ItemMeta meta = targetGun.getItemMeta();
                                if (meta != null && meta.hasLore()) {
                                    List<String> lore = meta.getLore();
                                    if (lore != null) {
                                        for (int i = 0; i < lore.size(); i++) {
                                            String line = lore.get(i);
                                            if (line.contains("总弹药")) {
                                                lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                                            }
                                        }
                                        meta.setLore(lore);
                                        targetGun.setItemMeta(meta);
                                        // 更新物品栏中的物品
                                        player.getInventory().setItem(targetSlot, targetGun);
                                    }
                                }
                            }

                            // 设置经验值为0，显示弹药耗尽状态
                            player.setLevel(0);
                            player.setExp(0);

                            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
                            isReloading.remove(player.getUniqueId()); // 确保移除换弹状态
                            cancel(); // 取消当前任务
                            return;
                        }

                        // 初始化弹夹数据结构
                        if (!clipAmmoMap.containsKey(player)) {
                            clipAmmoMap.put(player, new HashMap<>());
                        }

                        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

                        // 获取当前弹夹中的子弹数
                        int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

                        // 如果总弹药数和弹夹子弹数都为0，不允许换弹
                        if (totalAmmo <= 0 && currentClipAmmo <= 0) {
                            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
                            isReloading.remove(player.getUniqueId()); // 确保移除换弹状态
                            cancel(); // 取消当前任务
                            return;
                        }

                        // 如果总弹药数为0但弹夹中还有子弹，允许继续使用弹夹中的子弹
                        if (totalAmmo <= 0 && currentClipAmmo > 0) {
                            player.sendMessage(ChatColor.YELLOW + "总弹药已耗尽，但弹夹中还有" + currentClipAmmo + "发子弹！");
                            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                                    TextComponent.fromLegacyText(ChatColor.YELLOW + "总弹药已耗尽，弹夹中还有 " + currentClipAmmo + " 发子弹！"));
                            isReloading.remove(player.getUniqueId());
                            cancel();
                            return;
                        }

                        // 获取当前弹夹中的弹药数量
                        ItemStack currentGun = player.getInventory().getItemInMainHand();
                        if (!isGunItem(currentGun)) {
                            // 如果玩家手中不再是枪支，取消换弹
                            isReloading.remove(player.getUniqueId());
                            cancel();
                            return;
                        }

                        // 初始化弹夹数据结构
                        int clipSize = config.getInt("guns." + gunId + ".clip_size", 30);
                        int ammoNeeded = clipSize - currentClipAmmo; // 需要补充的弹药数量

                        if (ammoNeeded <= 0) {
                            // 弹夹已满，不需要换弹
                            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                                    TextComponent.fromLegacyText(ChatColor.GREEN + "弹夹已满！"));
                            isReloading.remove(player.getUniqueId());
                            cancel();
                            return;
                        }

                        // 方案二：换弹时从总弹药中扣除需要补充的子弹数量
                        // 计算需要从总弹药中扣除的数量：需要补充的子弹数量
                        int ammoToDeduct = ammoNeeded;

                        // 确保不会扣除超过总弹药的数量
                        ammoToDeduct = Math.min(ammoToDeduct, totalAmmo);

                        // 确保不会扣除负数弹药
                        if (ammoToDeduct < 0) {
                            ammoToDeduct = 0;
                        }

                        // 从总弹药中扣除
                        totalAmmo -= ammoToDeduct;
                        playerAmmo.put(gunIdKey, totalAmmo);

                        // 更新经验值显示
                        player.setLevel(totalAmmo);

                        // 计算经验条进度
                        int maxAmmo = config.getInt("guns." + gunId + ".ammo", 300);
                        float exp = (float) totalAmmo / maxAmmo;
                        if (exp > 1.0f) {
                            exp = 1.0f;
                        }
                        if (exp < 0.0f) {
                            exp = 0.0f;
                        }
                        player.setExp(exp);

                        // 计算换弹后的弹药数量
                        // 修复bug：如果总弹药数为0，不应该填满弹夹
                        int newClipAmmo = ammoToDeduct; // 使用实际扣除的弹药数量

                        // 更新弹夹中的子弹数
                        playerClipAmmo.put(gunId, newClipAmmo);

                        // 修复换弹完成时物品数量更新bug - 在物品栏中查找原始枪械物品并更新
                        ItemStack targetGun = null;
                        int targetSlot = -1;

                        // 遍历玩家物品栏查找匹配的枪械物品
                        for (int i = 0; i < player.getInventory().getSize(); i++) {
                            ItemStack item = player.getInventory().getItem(i);
                            if (item != null && isGunItem(item)) {
                                String itemDisplayName = item.getItemMeta() != null ? item.getItemMeta().getDisplayName() : "";
                                // 检查是否是相同的枪械（通过显示名称和类型比较）
                                if (item.getType() == originalGun.getType() &&
                                    itemDisplayName.equals(originalGunDisplayName)) {
                                    targetGun = item;
                                    targetSlot = i;
                                    break;
                                }
                            }
                        }

                        // 如果找到了原始枪械物品，更新其数量和属性
                        if (targetGun != null) {
                            // 更新物品数量显示，确保至少为1，防止物品消失
                            if (newClipAmmo <= 0) {
                                // 如果弹夹中的子弹数为0或负数，设置物品数量为1，防止物品消失
                                targetGun.setAmount(1);
                            } else {
                                // 否则，设置物品数量为当前弹夹中的子弹数
                                targetGun.setAmount(newClipAmmo);
                            }

                            // 更新物品描述中的当前弹药数量和总弹药数量显示
                            ItemMeta meta = targetGun.getItemMeta();
                            if (meta != null && meta.hasLore()) {
                                List<String> lore = meta.getLore();
                                if (lore != null) {
                                    for (int i = 0; i < lore.size(); i++) {
                                        String line = lore.get(i);
                                        if (line.contains("当前弹药")) {
                                            lore.set(i, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + newClipAmmo);
                                        } else if (line.contains("总弹药")) {
                                            // 使用更新后的总弹药数
                                            lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + totalAmmo);
                                        }
                                    }
                                    meta.setLore(lore);
                                }
                            }

                            // 恢复物品耐久度为满
                            if (meta instanceof Damageable) {
                                ((Damageable) meta).setDamage(0); // 设置为满耐久
                            }

                            targetGun.setItemMeta(meta);
                            // 更新物品栏中的物品
                            player.getInventory().setItem(targetSlot, targetGun);
                        }

                        // 播放换弹完成音效
                        playSoundSafely(player, player.getLocation(), "BLOCK_STONE_BUTTON_CLICK_ON", "BLOCK_STONE_BUTTON_CLICK_ON", 1.0f, 1.0f);

                        // 显示换弹完成提示
                        player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                                TextComponent.fromLegacyText(ChatColor.GREEN + "换弹完成！"));

                        // 强制更新玩家物品栏，确保物品数量更新
                        player.updateInventory();
                    }
                    isReloading.remove(player.getUniqueId());
                    cancel();
                    return;
                }

                // 计算进度
                double percentage = (double) progress / reloadTime;

                // 显示简化的换弹提示（只显示"换弹中"）
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.YELLOW + "换弹中"));

                // 播放换弹音效
                if (progress % 10 == 0) { // 每10个tick播放一次音效
                    playSoundSafely(player, player.getLocation(), "BLOCK_LEVER_CLICK", "BLOCK_LEVER_CLICK", 0.5f, 1.0f);
                }

                // 更新物品耐久度动画 - 修复切换物品时动画错误显示的bug
                // 在玩家物品栏中查找原始枪械物品并直接更新其耐久值
                ItemStack targetGun = null;
                int targetSlot = -1;

                // 遍历玩家物品栏查找匹配的枪械物品
                for (int i = 0; i < player.getInventory().getSize(); i++) {
                    ItemStack item = player.getInventory().getItem(i);
                    if (item != null && isGunItem(item)) {
                        String itemDisplayName = item.getItemMeta() != null ? item.getItemMeta().getDisplayName() : "";
                        // 检查是否是相同的枪械（通过显示名称和类型比较）
                        if (item.getType() == originalGun.getType() &&
                            itemDisplayName.equals(originalGunDisplayName)) {
                            targetGun = item;
                            targetSlot = i;
                            break;
                        }
                    }
                }

                // 如果找到了原始枪械物品，更新其耐久值动画
                if (targetGun != null) {
                    ItemMeta meta = targetGun.getItemMeta();
                    if (meta instanceof Damageable) {
                        Damageable damageable = (Damageable) meta;
                        // 计算耐久度（从1耐久到满耐久）
                        int maxDurability = targetGun.getType().getMaxDurability();
                        // 从最大耐久-1（几乎无耐久）开始，随着进度增加而减少损坏值（增加耐久度）
                        int damageValue = (int) ((1 - percentage) * (maxDurability - 1));
                        damageable.setDamage(damageValue);
                        targetGun.setItemMeta(meta);
                        // 更新物品栏中的物品
                        player.getInventory().setItem(targetSlot, targetGun);
                    }
                }

                progress++;
            }
        }.runTaskTimer(this, 0L, 1L);
    }

    // 更新弹药数量显示
    private void updateAmmoCount(Player player, String gunId, int ammoCount) {
        ItemStack gunItem = player.getInventory().getItemInMainHand();
        if (isGunItem(gunItem)) {
            // 确保物品数量至少为1，防止物品消失
            gunItem.setAmount(Math.max(1, Math.min(64, ammoCount)));
            player.getInventory().setItemInMainHand(gunItem);

            // 更新弹夹中的子弹数
            if (!clipAmmoMap.containsKey(player)) {
                clipAmmoMap.put(player, new HashMap<>());
            }
            Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);
            playerClipAmmo.put(gunId, Math.max(0, ammoCount));
        }
    }

    // 开火后更新弹药数量显示
    private void updateAmmoAfterShot(Player player, String gunId) {
        ItemStack gunItem = player.getInventory().getItemInMainHand();
        if (isGunItem(gunItem)) {
            int currentAmount = gunItem.getAmount();
            if (currentAmount >= 1) {  // 修改：从 currentAmount > 1 改为 currentAmount >= 1，允许减少到0
                // 减少弹夹中的弹药数量，但确保至少为1，防止物品消失
                int newAmount = Math.max(1, currentAmount - 1);
                gunItem.setAmount(newAmount);
                player.getInventory().setItemInMainHand(gunItem);

                // 获取当前总弹药数
                String gunIdKey = gunId + "_total";
                Map<String, Integer> playerAmmo = ammoMap.get(player);
                if (playerAmmo != null) {
                    int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

                    // 更新经验值显示（只用于显示总弹药数）
                    player.setLevel(totalAmmo);

                    // 更新物品描述中的当前弹药数量和总弹药数量显示
                    ItemMeta meta = gunItem.getItemMeta();
                    if (meta != null && meta.hasLore()) {
                        List<String> lore = meta.getLore();
                        if (lore != null) {
                            for (int i = 0; i < lore.size(); i++) {
                                String line = lore.get(i);
                                if (line.contains("当前弹药")) {
                                    lore.set(i, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + (currentAmount - 1));
                                }
                            }
                            meta.setLore(lore);
                            gunItem.setItemMeta(meta);
                        }
                    }
                }

                // 如果弹药已用完，在下一个tick自动换弹
                if (newAmount <= 1 && !isReloading.contains(player.getUniqueId())) {
                    // 获取总弹药数（使用已有的gunIdKey变量）
                    int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

                    // 获取弹夹中的子弹数
                    if (!clipAmmoMap.containsKey(player)) {
                        clipAmmoMap.put(player, new HashMap<>());
                    }
                    Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);
                    int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

                    // 如果总弹药数和弹夹子弹数都为0，不进行自动换弹
                    if (totalAmmo <= 0 && currentClipAmmo <= 0) {
                        player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                        player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                                TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
                        return;
                    }

                    // 使用延迟任务进行换弹，避免在当前操作中触发
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            // 确保玩家仍然在线且尚未在换弹
                            if (player.isOnline() && !isReloading.contains(player.getUniqueId())) {
                                reloadGun(player, gunId);
                            }
                        }
                    }.runTaskLater(this, 2L); // 延迟2tick执行换弹操作
                }
            }
        }
    }

    // 在射击方法中添加弹药数量更新
    private void handleSingleBullet(Player player, String gunId, Location startLoc, Vector direction) {
        new BukkitRunnable() {
            Location loc = startLoc.clone();
            int distance = 0;
            double maxDistance = 50;

            @Override
            public void run() {
                if (distance > maxDistance) {
                    cancel();
                    return;
                }

                // 移动子弹
                loc.add(direction.multiply(1));
                distance++;

                // 发送粒子效果
                particleHelper.displayParticle(loc, 0, 0, 0, 0, 2, Particle.CRIT);

                // 检测命中
                for (Entity entity : loc.getWorld().getNearbyEntities(loc, 0.5, 0.5, 0.5)) {
                    if (entity instanceof LivingEntity && entity != player) {
                        LivingEntity target = (LivingEntity) entity;
                        double damage = config.getDouble("guns." + gunId + ".damage", 5);
                        target.damage(damage, player);

                        // 命中特效
                        particleHelper.displayParticle(loc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);

                        // 播放命中音效
                        String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                        if (!hitSound.isEmpty()) {
                            playSoundSafely(player, loc, hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.0f);
                        }

                        cancel();
                        return;
                    }
                }
            }
        }.runTaskTimer(this, 0, 1);
    }

    private void handleMultipleBullets(Player player, String gunId, DisplaySettings settings) {
        // 检查是否有足够的弹药
        ItemStack gunItem = player.getInventory().getItemInMainHand();

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }

        // 初始化枪支状态数据结构
        if (!gunEmptyStatusMap.containsKey(player)) {
            gunEmptyStatusMap.put(player, new HashMap<>());
        }

        // 检查枪支是否处于弹药耗尽状态
        Map<String, Boolean> playerGunStatus = gunEmptyStatusMap.get(player);
        if (playerGunStatus.getOrDefault(gunId, false)) {
            // 如果枪支已经处于弹药耗尽状态，直接禁止射击
            player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                    TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
            return;
        }

        // 获取总弹药数
        String gunIdKey = gunId + "_total";
        Map<String, Integer> playerAmmo = ammoMap.get(player);
        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

        // 如果总弹药数小于1，检查弹夹中是否还有子弹
        if (totalAmmo < 1) {
            // 初始化弹夹数据结构
            if (!clipAmmoMap.containsKey(player)) {
                clipAmmoMap.put(player, new HashMap<>());
            }
            Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);
            int clipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

            // 修复bug：只有当弹夹中的子弹数为0且总子弹数为0时，才将枪支状态设置为弹药耗尽
            if (clipAmmo <= 0) {
                // 设置枪支状态为弹药耗尽
                playerGunStatus.put(gunId, true);

                // 获取枪支名称
                String gunName = "";
                if (isGunItem(gunItem) && gunItem.getItemMeta() != null) {
                    gunName = gunItem.getItemMeta().getDisplayName();
                }

                // 创建一个钻石物品
                ItemStack emptyGun = new ItemStack(Material.DIAMOND);
                ItemMeta emptyMeta = emptyGun.getItemMeta();
                if (emptyMeta != null) {
                    // 设置显示名称
                    emptyMeta.setDisplayName(ChatColor.RED + "弹药耗尽的武器");

                    // 设置Lore
                    List<String> lore = new ArrayList<>();
                    lore.add(ChatColor.GRAY + "原武器: " + ChatColor.YELLOW + gunName);
                    lore.add(ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                    lore.add(ChatColor.GRAY + "当前弹药: " + ChatColor.RED + "0");
                    lore.add("");
                    lore.add(ChatColor.RED + "弹药已耗尽，无法射击");
                    lore.add(ChatColor.YELLOW + "请使用 /shoot up 补充弹药");

                    // 存储原始枪支ID，用于恢复
                    lore.add(ChatColor.BLACK + "gun_id:" + gunId);

                    emptyMeta.setLore(lore);
                    emptyGun.setItemMeta(emptyMeta);
                }

                // 替换玩家手中的枪支
                player.getInventory().setItemInMainHand(emptyGun);

                // 设置经验值为0，显示弹药耗尽状态
                player.setLevel(0);
                player.setExp(0);

                player.sendMessage(ChatColor.RED + "弹药完全耗尽！请补充弹药！");
                player.spigot().sendMessage(ChatMessageType.ACTION_BAR,
                        TextComponent.fromLegacyText(ChatColor.RED + "弹药完全耗尽！"));
                return;
            } else {
                // 更新物品描述中的总弹药显示为耗尽，但不显示提示信息
                if (isGunItem(gunItem)) {
                    ItemMeta meta = gunItem.getItemMeta();
                    if (meta != null && meta.hasLore()) {
                        List<String> lore = meta.getLore();
                        if (lore != null) {
                            for (int i = 0; i < lore.size(); i++) {
                                String line = lore.get(i);
                                if (line.contains("总弹药")) {
                                    lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.RED + "耗尽");
                                }
                            }
                            meta.setLore(lore);
                            gunItem.setItemMeta(meta);
                        }
                    }
                }
            }
        }

        // 检查是否需要换弹
        if (!isGunItem(gunItem) || gunItem.getAmount() < 1) {
            // 检查是否有总弹药可以补充
            if (totalAmmo > 0) {
                reloadGun(player, gunId);
            }
            return;
        }

        // 这部分代码已经在前面处理过了，不需要重复
        // 方案二：开枪时不减少总弹药数，只减少弹夹中的子弹数
        // 使用updatePlayerXP方法更新经验值显示
        updatePlayerXP(player, gunId);

        // 这部分代码已经在前面处理过了，不需要重复
        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());
        }
        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);
        int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, 0);

        // 减少弹夹中的子弹数
        currentClipAmmo--;
        playerClipAmmo.put(gunId, currentClipAmmo);

        // 物品数量显示当前弹夹中的子弹数，但确保至少为1，防止物品消失
        if (currentClipAmmo <= 0) {
            // 如果弹夹中的子弹数为0或负数，设置物品数量为1，防止物品消失
            gunItem.setAmount(1);
        } else {
            // 否则，设置物品数量为当前弹夹中的子弹数
            gunItem.setAmount(currentClipAmmo);
        }
        player.getInventory().setItemInMainHand(gunItem);

        // 更新物品描述中的当前弹药数量和总弹药数量显示
        ItemMeta meta = gunItem.getItemMeta();
        if (meta != null && meta.hasLore()) {
            List<String> lore = meta.getLore();
            if (lore != null) {
                for (int i = 0; i < lore.size(); i++) {
                    String line = lore.get(i);
                    if (line.contains("当前弹药")) {
                        lore.set(i, ChatColor.GRAY + "当前弹药: " + ChatColor.YELLOW + (gunItem.getAmount()));
                    } else if (line.contains("总弹药")) {
                        lore.set(i, ChatColor.GRAY + "总弹药: " + ChatColor.YELLOW + totalAmmo);
                    }
                }
                meta.setLore(lore);
                gunItem.setItemMeta(meta);
            }
        }

        // 获取霰弹枪和机枪的配置
        int pelletCount;
        double spreadAngle;

        if (gunId.equals("id3")) { // 霰弹枪
            pelletCount = 24; // 霰弹枪发射24发
            spreadAngle = 25; // 霰弹枪扩散角度更大
        } else if (gunId.equals("id4")) { // 机枪
            pelletCount = 16; // 机枪发射16发
            spreadAngle = 10; // 机枪扩散角度较小
        } else {
            // 默认值，以防有其他武器使用这个方法
            pelletCount = 16;
            spreadAngle = 10;
        }

        for (int i = 0; i < pelletCount; i++) {
            // 计算每条粒子的方向
            double horizontalAngle = Math.random() * spreadAngle - spreadAngle / 2;
            double verticalAngle = Math.random() * spreadAngle - spreadAngle / 2;
            Location loc = player.getEyeLocation().clone();

            // 应用水平和垂直旋转
            org.bukkit.util.Vector direction = loc.getDirection().clone();
            direction = rotateVectorAroundY(direction, Math.toRadians(horizontalAngle));
            direction = rotateVector(direction, loc.getDirection().clone().crossProduct(new org.bukkit.util.Vector(0, 1, 0)), Math.toRadians(verticalAngle));
            loc.setDirection(direction);

            // 启动子弹轨迹任务
            new BukkitRunnable() {
                Location bulletLoc = loc.clone();
                int distance = 0; // 子弹飞行距离
                double maxDistance = gunId.equals("id3") ? 30 : 50; // 霰弹枪射程较短

                @Override
                public void run() {
                    if (distance > maxDistance) {
                        // 达到最大距离，取消任务
                        cancel();
                        return;
                    }

                    bulletLoc = bulletLoc.add(bulletLoc.getDirection().multiply(1));

                    // 根据枪支类型显示不同粒子效果
                    if (gunId.equals("id3")) { // 霰弹枪 - 红色粒子
                        bulletLoc.getWorld().spawnParticle(
                                Particle.DUST,
                                bulletLoc,
                                2,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(org.bukkit.Color.RED, 1.0f)
                        );
                    } else if (gunId.equals("id4")) { // 机枪 - 白色粒子
                        bulletLoc.getWorld().spawnParticle(
                                Particle.DUST,
                                bulletLoc,
                                2,
                                0, 0, 0,
                                0,
                                new Particle.DustOptions(org.bukkit.Color.WHITE, 1.0f)
                        );
                    }

                    // 检查是否击中方块
                    if (!bulletLoc.getBlock().isPassable() && !shouldIgnoreBlocks(gunId)) {
                        particleHelper.displayParticle(bulletLoc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);
                        particleHelper.displayParticle(bulletLoc, 0.1f, 0.1f, 0.1f, 0, 10, Particle.CLOUD);
                        playSoundSafely(player, bulletLoc, "BLOCK_STONE_HIT", "BLOCK_STONE_HIT", 1.0f, 1.0f);
                        cancel();
                        return;
                    }

                    // 检测命中
                    for (Entity entity : bulletLoc.getWorld().getNearbyEntities(bulletLoc, 0.5, 0.5, 0.5)) {
                        if (entity instanceof LivingEntity && entity != player) {
                            LivingEntity target = (LivingEntity) entity;

                            // 获取伤害值
                            double damage = config.getDouble("guns." + gunId + ".damage", 5);
                            target.damage(damage, player);

                            // 命中特效
                            particleHelper.displayParticle(bulletLoc, 0.2f, 0.2f, 0.2f, 0, 15, Particle.CRIT);

                            // 播放命中音效
                            String hitSound = config.getString("guns." + gunId + ".shoot_sound_hit", "");
                            if (!hitSound.isEmpty()) {
                                playSoundSafely(player, bulletLoc, hitSound, "ENTITY_ARROW_HIT", 1.0f, 1.0f);
                            }

                            // 显示命中提示
                            if (settings != null && settings.isHitTitle()) {
                                sendTitle(player, 5, 20, 5, ChatColor.RED + "击中！", ChatColor.YELLOW + "你击中了一个目标！");
                            }

                            // 取消子弹飞行
                            cancel();
                            return;
                        }
                    }
                    distance++;
                }
            }.runTaskTimer(this, 0, 1); // 每1 tick更新一次
        }
    }

    // 检查物品是否为枪支
    private boolean isGunItem(ItemStack item) {
        if (item == null) {
            return false;
        }

        // 检查物品是否有元数据和显示名称
        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }

        // 获取物品显示名称
        String displayName = ChatColor.stripColor(meta.getDisplayName());

        // 检查是否是弹药耗尽的武器（钻石）
        if (item.getType() == Material.DIAMOND && displayName.contains("弹药耗尽的武器")) {
            // 检查是否有lore中包含gun_id标记
            if (meta.hasLore()) {
                List<String> lore = meta.getLore();
                if (lore != null) {
                    for (String line : lore) {
                        if (line.contains("gun_id:")) {
                            return true; // 这是一个弹药耗尽的武器
                        }
                    }
                }
            }
        }

        // 检查是否是已定义的枪支
        ConfigurationSection gunsSection = config.getConfigurationSection("guns");
        if (gunsSection != null) {
            for (String id : gunsSection.getKeys(false)) {
                String gunName = config.getString("guns." + id + ".name", "未知武器");
                if (displayName.equals(gunName)) {
                    return true;
                }
            }
        }

        // 检查物品类型是否匹配配置中的枪支材质
        Material itemType = item.getType();

        if (gunsSection != null) {
            for (String id : gunsSection.getKeys(false)) {
                String materialName = config.getString("guns." + id + ".material", "");
                if (!materialName.isEmpty()) {
                    Material gunMaterial = Material.getMaterial(materialName);
                    if (gunMaterial != null && gunMaterial == itemType) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    // 获取玩家弹药数量
    private int getPlayerAmmo(Player player, String gunId) {
        // 获取对应的弹药ID
        String ammoId = getAmmoIdForGun(gunId);
        if (ammoId == null) {
            return 0;
        }
        return getPlayerExp(player);
    }

    // 完成换弹过程
    private void finishReload(Player player, String gunId, int maxAmmo) {
        // 播放换弹完成音效
        playSoundSafely(player, player.getLocation(), "BLOCK_STONE_BUTTON_CLICK_ON", "BLOCK_STONE_BUTTON_CLICK_ON", 1.0f, 1.0f);

        // 更新弹药显示
        updateAmmoCount(player, gunId, maxAmmo);
    }

    // 获取枪支对应的弹药ID
    private String getAmmoIdForGun(String gunId) {
        // 枪支和弹药的对应关系
        Map<String, String> gunToAmmo = new HashMap<>();
        gunToAmmo.put("id1", "id3");  // 手枪 (现在id1是手枪)
        gunToAmmo.put("id2", "id4");  // 制式步枪
        gunToAmmo.put("id3", "id5");  // 霰弹枪
        gunToAmmo.put("id4", "id6");  // 重型机枪
        gunToAmmo.put("id5", "id7");  // 火箭筒
        gunToAmmo.put("id6", "id8");  // 电击枪
        gunToAmmo.put("id7", "id9");  // 狙击步枪
        gunToAmmo.put("id8", "id10"); // 冷冻枪
        gunToAmmo.put("id9", "id11"); // 雷击枪
        gunToAmmo.put("id10", "id12"); // 压强枪
        gunToAmmo.put("id11", "id4");  // 突击步枪 (现在id11是突击步枪)
        gunToAmmo.put("id13", "id16"); // 等离子枪
        gunToAmmo.put("id14", "id17"); // 死神收割者
        gunToAmmo.put("id15", "id18"); // 毁灭者
        gunToAmmo.put("id16", "id19"); // 超级激光炮
        gunToAmmo.put("id17", "id20"); // 黑洞吞噬者
        gunToAmmo.put("id18", "id21"); // 音波步枪
        gunToAmmo.put("id19", "id22"); // 能量脉冲枪
        gunToAmmo.put("id20", "id23"); // 彩虹喷射器
        gunToAmmo.put("id21", "id24"); // 传送枪
        gunToAmmo.put("id22", "id25"); // 星辉主宰者
        gunToAmmo.put("id23", "id26"); // 虚空星尘使者
        gunToAmmo.put("id24", "id27"); // 循声炮

        return gunToAmmo.get(gunId);
    }

    // 获取玩家总弹药数
    private int getPlayerExp(Player player) {
        String gunId = currentGunMap.get(player);
        if (gunId == null) {
            return 0;
        }

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }

        // 初始化枪支弹药数据
        Map<String, Integer> playerAmmo = ammoMap.get(player);
        // 不再自动初始化弹药数据，避免弹药自动恢复
        // 如果没有弹药数据，返回0
        if (!playerAmmo.containsKey(gunId + "_total")) {
            return 0;
        }

        // 返回总弹药数
        return playerAmmo.getOrDefault(gunId + "_total", 0);
    }

    // 设置玩家总弹药数
    private void setPlayerExp(Player player, int exp) {
        String gunId = currentGunMap.get(player);
        if (gunId == null) {
            return;
        }

        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }

        // 更新总弹药数
        ammoMap.get(player).put(gunId + "_total", exp);

        // 更新经验值显示（只用于显示）
        player.setLevel(exp);
    }

    /**
     * 内部类：存储武器弹药信息
     */
    private static class AmmoInfo {

        private int clipAmmo;      // 弹夹中的弹药
        private int totalAmmo;     // 总弹药量

        public AmmoInfo(int clipAmmo, int totalAmmo) {
            this.clipAmmo = clipAmmo;
            this.totalAmmo = totalAmmo;
        }

        public int getClipAmmo() {
            return clipAmmo;
        }

        public void setClipAmmo(int clipAmmo) {
            this.clipAmmo = clipAmmo;
        }

        public int getTotalAmmo() {
            return totalAmmo;
        }

        public void setTotalAmmo(int totalAmmo) {
            this.totalAmmo = totalAmmo;
        }
    }

    /**
     * 获取武器的弹药信息
     */
    private AmmoInfo getAmmoInfo(Player player, String gunId) {
        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }
        Map<String, Integer> playerAmmo = ammoMap.get(player);

        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());
        }
        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

        // 从配置获取最大弹夹容量
        int maxClipSize = config.getInt("guns." + gunId + ".clip_size", 30);

        // 获取当前弹夹中的弹药 - 修复：从clipAmmoMap中获取，而不是从ammoMap中获取
        int currentClipAmmo = playerClipAmmo.getOrDefault(gunId, maxClipSize);

        // 从ammoMap中获取总弹药量，而不是从玩家经验等级获取
        String gunIdKey = gunId + "_total";
        int totalAmmo = playerAmmo.getOrDefault(gunIdKey, 0);

        return new AmmoInfo(currentClipAmmo, totalAmmo);
    }

    /**
     * 设置武器的弹药信息
     */
    private void setAmmoInfo(Player player, String gunId, AmmoInfo ammoInfo) {
        // 初始化弹药数据结构
        if (!ammoMap.containsKey(player)) {
            ammoMap.put(player, new HashMap<>());
        }
        Map<String, Integer> playerAmmo = ammoMap.get(player);

        // 初始化弹夹数据结构
        if (!clipAmmoMap.containsKey(player)) {
            clipAmmoMap.put(player, new HashMap<>());
        }
        Map<String, Integer> playerClipAmmo = clipAmmoMap.get(player);

        // 更新弹夹中的弹药 - 修复：使用clipAmmoMap而不是ammoMap
        playerClipAmmo.put(gunId, ammoInfo.getClipAmmo());

        // 更新总弹药量(同时更新ammoMap和玩家经验等级)
        playerAmmo.put(gunId + "_total", ammoInfo.getTotalAmmo());
        player.setLevel(ammoInfo.getTotalAmmo());

        // 更新经验条显示
        updatePlayerXP(player, gunId);
    }

    /**
     * Tab补全功能
     */
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (command.getName().equalsIgnoreCase("byr")) {
            if (args.length == 1) {
                // 第一个参数：子命令（根据权限显示不同的命令）
                List<String> subCommands = new ArrayList<>();

                // 所有玩家都可以使用的命令
                subCommands.addAll(Arrays.asList("help", "check"));

                // 管理员专用命令
                if (sender.hasPermission("shoot.admin")) {
                    subCommands.addAll(Arrays.asList("set", "remove", "buy", "list", "add"));
                }

                for (String subCommand : subCommands) {
                    if (subCommand.toLowerCase().startsWith(args[0].toLowerCase())) {
                        completions.add(subCommand);
                    }
                }
            } else if (args.length == 2) {
                String subCommand = args[0].toLowerCase();
                if (subCommand.equals("set")) {
                    // set命令的第二个参数：物品类型（中文名称优先）
                    List<String> types = Arrays.asList("护甲", "武器", "道具", "特殊功能");
                    for (String type : types) {
                        if (type.toLowerCase().startsWith(args[1].toLowerCase())) {
                            completions.add(type);
                        }
                    }
                }
            } else if (args.length == 3) {
                String subCommand = args[0].toLowerCase();
                if (subCommand.equals("set")) {
                    // set命令的第三个参数：物品ID或中文名称（只显示中文名称）
                    String inputType = args[1];
                    String type = getTypeByInput(inputType);
                    if (type != null) {
                        ConfigurationSection typeSection = buyConfig.getConfigurationSection(type);
                        if (typeSection != null) {
                            for (String itemId : typeSection.getKeys(false)) {
                                String itemName = buyConfig.getString(type + "." + itemId + ".name", "");
                                // 只添加中文名称补全
                                if (itemName.toLowerCase().startsWith(args[2].toLowerCase())) {
                                    completions.add(itemName);
                                }
                            }
                        }
                    }
                } else if (subCommand.equals("add") && sender.hasPermission("shoot.admin")) {
                    // add命令的第三个参数：玩家名称
                    for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                        String playerName = onlinePlayer.getName();
                        if (playerName.toLowerCase().startsWith(args[2].toLowerCase())) {
                            completions.add(playerName);
                        }
                    }
                }
            }
        } else if (command.getName().equalsIgnoreCase("shoot")) {
            if (args.length == 1) {
                // 第一个参数：子命令（根据权限显示不同的命令）
                List<String> subCommands = new ArrayList<>();

                // 普通玩家可用的命令
                subCommands.addAll(Arrays.asList("help", "display"));

                // 管理员专用命令
                if (sender.hasPermission("shoot.admin")) {
                    subCommands.addAll(Arrays.asList("get", "up", "gui", "reload"));
                }

                for (String subCommand : subCommands) {
                    if (subCommand.toLowerCase().startsWith(args[0].toLowerCase())) {
                        completions.add(subCommand);
                    }
                }
            } else if (args.length == 2) {
                String subCommand = args[0].toLowerCase();
                if (subCommand.equals("get") && sender.hasPermission("shoot.admin")) {
                    // get命令的第二个参数：优先提供中文名称补全（仅管理员）
                    ConfigurationSection gunsSection = config.getConfigurationSection("guns");
                    if (gunsSection != null) {
                        for (String gunId : gunsSection.getKeys(false)) {
                            String gunName = config.getString("guns." + gunId + ".name", "");

                            // 只添加中文名称补全，更适合玩家使用
                            if (gunName.toLowerCase().startsWith(args[1].toLowerCase())) {
                                completions.add(gunName);
                            }
                        }
                    }
                } else if (subCommand.equals("display")) {
                    // display命令的第二个参数：显示类型
                    List<String> displayTypes = Arrays.asList("start", "hit");
                    for (String type : displayTypes) {
                        if (type.toLowerCase().startsWith(args[1].toLowerCase())) {
                            completions.add(type);
                        }
                    }
                }
            } else if (args.length == 3) {
                String subCommand = args[0].toLowerCase();
                if (subCommand.equals("display")) {
                    // display命令的第三个参数：开关
                    List<String> displayActions = Arrays.asList("on", "off");
                    for (String action : displayActions) {
                        if (action.toLowerCase().startsWith(args[2].toLowerCase())) {
                            completions.add(action);
                        }
                    }
                }
            }
        }

        return completions;
    }

    /**
     * 检查是否是插件GUI界面
     *
     * @param view InventoryView对象
     * @return 如果是插件GUI界面则返回true
     */
    private boolean isPluginGUI(InventoryView view) {
        if (view == null) {
            return false;
        }

        String title = view.getTitle();
        if (title == null) {
            return false;
        }

        // 检查是否是Shoot插件的GUI界面
        if (title.equals(ChatColor.BLUE + "枪支和弹药") ||
            title.equals(ChatColor.BLUE + "购买物品")) {
            return true;
        }

        // 检查是否是DeathZombieV4的KitGUI相关界面
        return isDeathZombieKitGUI(title);
    }

}
